<template>
  <div>
    <div class="form-row">
      <label class="form-label">页面标题</label>
      <el-radio-group class="radio-group" v-model="visualStore.pageConfig.pageLayout.pageTitle">
        <el-radio v-for="item in titleOptions" :key="item.value" :value="item.value">
          {{ item.label }}
        </el-radio>
      </el-radio-group>
    </div>

    <div class="form-row">
      <label class="form-label">页面背景</label>
      <SelectColor ref="selectColorRef" :initialColor="getInitialColor()" :initialMode="visualStore.pageConfig.pageLayout.colorMode" @update:color="handleColorUpdate" @modeChange="handleModeChange" />
    </div>

    <div class="form-row">
      <label class="form-label">顶部图片</label>
      <ImageSelector v-model="visualStore.pageConfig.pageLayout.pageTopImg" :imgList="imgList" label="顶部图片" />
    </div>

    <div class="form-row">
      <label class="form-label">底部图片</label>
      <ImageSelector v-model="visualStore.pageConfig.pageLayout.pageBottomImg" :imgList="imgList" label="底部图片" />
    </div>

    <div class="form-row">
      <label class="form-label">页面边距</label>
      <div class="padding-inputs">
        <div class="input-row">
          <el-icon><Monitor /></el-icon>
          <span>上</span>
          <el-input v-model.number="top" class="mini-input" size="small" />
          <span class="unit">px</span>
          <el-icon><Monitor /></el-icon>
          <span>左</span>
          <el-input v-model.number="left" class="mini-input" size="small" />
          <span class="unit">px</span>
        </div>
        <div class="input-row">
          <el-icon><Monitor /></el-icon>
          <span>下</span>
          <el-input v-model.number="bottom" class="mini-input" size="small" />
          <span class="unit">px</span>
          <el-icon><Monitor /></el-icon>
          <span>右</span>
          <el-input v-model.number="right" class="mini-input" size="small" />
          <span class="unit">px</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, watch, onMounted} from 'vue';
import {useVisualStore} from '@/store/modules/visual';
import {Monitor} from '@element-plus/icons-vue';
import SelectColor from '@/components/SelectColor/SelectColor.vue';
import ImageSelector from '@/components/ImageSelector/ImageSelector.vue';

const visualStore = useVisualStore();
const selectColorRef = ref(null);

const titleOptions = [
  {label: '开启', value: 'show'},
  {label: '关闭', value: 'none'},
];

const top = ref(10);
const right = ref(12);
const bottom = ref(10);
const left = ref(12);

const imgList = [
  {url: '/src/assets/imgs/visual/pageBg1.png'},
  {url: '/src/assets/imgs/visual/pageBg2.png'},
  {url: '/src/assets/imgs/visual/pageBg3.png'},
  {url: '/src/assets/imgs/visual/pageBg4.png'},
  {url: '/src/assets/imgs/visual/pageBg5.jpg'},
  {url: '/src/assets/imgs/visual/pageBg6.jpg'},
];

// 初始化 padding
onMounted(() => {
  const val = visualStore.pageConfig.pageLayout.pagePadding;
  const arr = val.split(' ');
  if (arr.length === 4) {
    top.value = parseInt(arr[0]);
    right.value = parseInt(arr[1]);
    bottom.value = parseInt(arr[2]);
    left.value = parseInt(arr[3]);
  }
});

// 监听边距变化
watch([top, right, bottom, left], ([t, r, b, l]) => {
  visualStore.pageConfig.pageLayout.pagePadding = `${t}px ${r}px ${b}px ${l}px`;
});

// 处理颜色更新事件
function handleColorUpdate(color) {
  // 根据当前模式保存到对应的字段
  if (visualStore.pageConfig.pageLayout.colorMode === 'gradient') {
    // 渐变色模式，保存到gradientColor字段
    visualStore.pageConfig.pageLayout.gradientColor = color;
  } else {
    // 纯色模式，保存到pageBackgroundColor字段
    visualStore.pageConfig.pageLayout.pageBackgroundColor = color;
  }
  console.log('页面背景颜色已更新:', color);
}

// 处理模式变化事件
function handleModeChange(mode) {
  // 将颜色模式保存到store中
  visualStore.pageConfig.pageLayout.colorMode = mode;
  console.log('颜色模式已切换:', mode);
}

// 获取初始颜色值
function getInitialColor() {
  const mode = visualStore.pageConfig.pageLayout.colorMode;
  if (mode === 'gradient') {
    // 渐变模式，返回渐变色值
    return visualStore.pageConfig.pageLayout.gradientColor;
  } else {
    // 纯色模式，返回纯色值
    return visualStore.pageConfig.pageLayout.pageBackgroundColor;
  }
}
</script>

<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;

  .form-label {
    width: 80px;
    color: #222;
    font-size: 14px;
    flex-shrink: 0;
  }

  .radio-group,
  .color-picker,
  .image-upload-placeholder,
  .image-upload-placeholder-img {
    margin-left: 8px;
  }

  .image-upload-placeholder,
  .image-upload-placeholder-img {
    width: 100px;
    height: 70px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ddd;

    .plus {
      font-size: 32px;
      color: #666;
      font-weight: bold;
    }
  }

  .image-upload-placeholder-img {
    object-fit: cover;
    background: none;
  }
}

// Popover 内部图片选择
:deep(.el-tabs__content) {
  .image-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 135px;

    .image-list {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 10px;

      .image-list-item {
        width: 109px;
        height: 50px;
        border: 3px solid transparent;
        padding: 2px;
        box-sizing: border-box;
        border-radius: 4px;
        transition: border-color 0.2s;

        &.selected-img-border {
          border-color: #409eff;
        }
      }
    }
  }
}

// Popover 底部清空按钮
.image-popover-footer {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ccc;
  text-align: right;
}

// 边距输入框
.padding-inputs {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .input-row {
    display: flex;
    align-items: center;
    gap: 4px;

    span {
      font-size: 13px;
      margin: 0 2px;
    }

    .mini-input {
      width: 60px;
    }

    .unit {
      color: #666;
      font-size: 12px;
      margin-left: 2px;
      margin-right: 8px;
    }
  }
}

// 上传模块
.upload-demo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  :deep(.el-upload-dragger) {
    border: none;
    padding: 0;
    text-align: left;
  }
}

.upload-tip {
  text-align: left;
  color: #888;
  font-size: 13px;
  margin-top: 15px;
}

.upload-link-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;

  .upload-link-input {
    flex: 1;
  }
}
</style>
