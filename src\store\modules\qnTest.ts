import {defineStore} from 'pinia';
import {store} from '@/store';

interface ParamsForm {
  type: 'view' | 'add' | 'verify';
  selectList?: any[];
  id?: string;
}

export const useQnTestStore = defineStore('qnTest', {
  // 正确导出方式
  state: () => ({
    paramsForm: {} as ParamsForm,
  }),
  actions: {
    setAddQnTest(params: any) {
      this.paramsForm = params;
    },
    clearQnTest() {
      this.paramsForm = {};
    },
  },
  // 新增持久化配置
  persist: [
    {
      key: 'QN_TEST_STORE', // 存储的key
      storage: localStorage, // 选择存储介质
      paths: ['paramsForm'], // 指定要持久化的state字段
    },
  ],
});

export const useQnTestStoreWithOut = () => {
  return useQnTestStore(store);
};
