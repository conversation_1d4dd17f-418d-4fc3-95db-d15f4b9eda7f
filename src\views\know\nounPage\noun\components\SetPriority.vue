<template>
  <el-dialog v-model="dialogVisible" title="设置优先级" width="30%" :before-close="handleClose">
    <div class="priority-setting">
      <h3>说明</h3>
      <p class="description">智能助手会根据用户的名词定义生成指标体系，不同分组的名词会出现重复的情况造成理解冲突，请配置名词分组的优先级！</p>

      <el-table :data="priorityData" style="width: 100%; margin-top: 15px">
        <el-table-column prop="name" label="分组名称" />
        <el-table-column label="优先级">
          <template #default="scope">
            {{ scope.row.priority }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <div class="operation-buttons">
              <el-button link type="primary" :disabled="scope.$index === 0" @click="moveUp(scope.$index)">上移</el-button>
              <el-button link type="primary" :disabled="scope.$index === priorityData.length - 1" @click="moveDown(scope.$index)">下移</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {ref, reactive} from 'vue';
import {ElMessage} from 'element-plus';
import * as api from '@/api/know/noun/index';

const emit = defineEmits(['getGroupData']);

const dialogVisible = ref<boolean>(false);
const priorityData = reactive<any[]>([]);

const getGroupData = async () => {
  try {
    const res = await api.groupPage({pageSize: '100', pageNo: '1'});
    priorityData.length = 0;
    priorityData.push(
      ...res.list
        .map((item: any) => ({
          id: item.id,
          name: item.name,
          priority: item.priority,
        }))
        .sort((a: any, b: any) => a.priority - b.priority)
    );
  } catch (error) {
    console.log('error', error);
  }
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleCancel = () => {
  dialogVisible.value = false;
};

const moveUp = (index: number) => {
  if (index > 0) {
    const temp = priorityData[index - 1];
    priorityData[index - 1] = priorityData[index];
    priorityData[index] = temp;

    // 新增代码：根据新位置更新优先级
    priorityData.forEach((item, i) => {
      item.priority = i + 1;
    });
  }
};

const moveDown = (index: number) => {
  if (index < priorityData.length - 1) {
    const temp = priorityData[index + 1];
    priorityData[index + 1] = priorityData[index];
    priorityData[index] = temp;

    // 新增代码：根据新位置更新优先级
    priorityData.forEach((item, i) => {
      item.priority = i + 1;
    });
  }
};

const handleSubmit = async () => {
  try {
    // 新增提交逻辑：遍历所有分组提交更新
    for (const [index, item] of priorityData.entries()) {
      await api.groupUpdate({
        ...item,
        priority: index + 1, // 使用当前索引+1作为新优先级
      });
    }

    ElMessage.success('保存成功');
    emit('getGroupData');
    dialogVisible.value = false;
  } catch (error) {
    ElMessage.error('保存失败');
    console.error(error);
  }
};

const openDialog = () => {
  getGroupData();
  dialogVisible.value = true;
};

defineExpose({openDialog});
</script>

<style scoped lang="scss">
.priority-setting {
  padding: 0 20px;
}

h3 {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}

.description {
  margin-bottom: 20px;
  line-height: 1.6;
  color: #606266;
}

.operation-buttons {
  display: flex;
  justify-content: space-evenly;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
