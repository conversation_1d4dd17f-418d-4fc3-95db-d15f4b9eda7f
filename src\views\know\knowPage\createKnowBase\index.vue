<template>
  <PageHeader title="知识库">
    <template #page-header-back>
      <el-icon class="header-back-icon" @click="handleCancel"><ArrowLeftBold /></el-icon>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="知识库名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入知识库名称" :disabled="dialogType == 'addFeedback'" />
        </el-form-item>

        <el-form-item label="知识库描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="2" placeholder="请输入知识库描述" :disabled="dialogType == 'addFeedback'" />
        </el-form-item>

        <el-form-item label="导入方式" prop="importMethod" style="width: 80%">
          <div style="width: 100%">
            <div class="import-type-prompt">按照文件类型导入</div>
          </div>
          <div class="import-type-container">
            <div class="import-type" @click="handleImportTypeChange('1')" :class="{'import-type-active': form.importMethod === '1'}">
              <div class="import-type-title">导入文本文档数据</div>
              <div class="import-type-description">根据上传的文本文件直接进行切分处理</div>
            </div>
            <div class="import-type" @click="handleImportTypeChange('2')" :class="{'import-type-active': form.importMethod === '2'}">
              <div class="import-type-title">导入表格型知识数据</div>
              <div class="import-type-description">
                <p>读取表格中的文本信息，按行构建知识切片。更适用于含有长文的表格（表格单元格中存储了较长的文本内容；可按列索引返回整行内容作为切片</p>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :on-change="handleChange"
          :on-exceed="handleExceed"
          :auto-upload="false"
          :limit="20"
          multiple
          accept=".xlsx,.xls,.csv,.jsonl"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <p>单次上传文档数量不超过20个；支持.xlsx/.xls/.csv/.jsonl四种文件格式；</p>
          <p>支持UTF-8、GBK、GB2312、GB18030、ASCII五种编码格式；</p>
          <p>单个文件大小不超过100MB，不超过10万行，20列，每行不超过15万字，</p>
          <p>且文件中最多支持一个sheet工作表（超出范围的内容会被自动忽略）</p>
          <div v-if="form.importMethod === '2'" style="margin-top: 20px">
            <span class="upload-demo-tip">模版下载</span>
            <el-button v-for="template in templateButtonList" :key="template.type" @click.stop="downloadTemplate(template.type)" text bg :style="{marginLeft: '20px', color: template.color}">
              {{ template.label }}
            </el-button>
          </div>
        </el-upload>
        <el-form-item label="解析策略" prop="analysisStrategy">
          <el-checkbox v-for="item in parseModeList" :key="item.code" v-model="item.checked">
            {{ item.message }}
            <span class="desc">{{ item.desc }}</span>
          </el-checkbox>
        </el-form-item>
      </el-form>

      <div class="content-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">创建</el-button>
      </div>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import {ref, onMounted, reactive, toRaw} from 'vue';
import {ElMessage} from 'element-plus';
import {UploadFilled, ArrowLeftBold} from '@element-plus/icons-vue';
import * as api from '@/api/know/knowPage/knowBase/index';
import {useRouter} from 'vue-router';
import PageHeader from '@/components/PageHeader.vue';
import {useKnowStore} from '@/store/modules/know';
import type {UploadFile} from 'element-plus';

const knowStore = useKnowStore();
const router = useRouter();
const formRef = ref<HTMLFormElement | null>(null);
const fileList = ref<UploadFile[]>([]);
const dialogType = ref<string>('add');
const editId = ref<string>('');
const uploadRef = ref<HTMLInputElement | null>(null);

// 定义表单验证规则
const rules = {
  name: [{required: true, message: '请输入知识库名称', trigger: 'blur'}],
  importMethod: [{required: true, message: '请选择导入方式', trigger: 'change'}],
  analysisStrategy: [
    {
      required: true,
      validator: (_rule: any, _value: any, callback: any) => {
        const checkedCount = parseModeList.value.filter(item => item.checked).length;
        if (checkedCount === 0) {
          callback(new Error('请至少选择一个解析策略'));
        } else {
          callback();
        }
      },
      trigger: 'change',
    },
  ],
};

// 第一行选项数据
let parseModeList = ref<any[]>([]);

// 下载模版按钮
const templateButtonList = [
  {label: 'csv 模版', type: 'csv', color: '#528DFF'},
  {label: 'xls 模版', type: 'xls', color: '#02BFBF'},
  {label: 'xlsx 模版', type: 'xlsx', color: '#7073FF'},
  {label: 'jsonl 模版', type: 'jsonl', color: '#F25788'},
];

const form = ref<any>({
  name: '',
  description: '',
  importMethod: '1',
});

onMounted(() => {
  initData(knowStore.knowParamsForm.type, knowStore.knowParamsForm);
  getParseModeList();
});

const getParseModeList = async () => {
  try {
    const res = await api.analysisStrategyTypeList();
    parseModeList.value = res;
  } catch (error) {
    console.log('[ error ] >', error);
  }
};

// 下载模版
const downloadTemplate = (type: string) => {
  const link = document.createElement('a');
  link.href = `/templates/${type}_template.${type}`;
  link.download = `${type}_template.${type}`;
  link.click();
};

// 切换导入方式
const handleImportTypeChange = (method: string) => {
  form.value.importMethod = method;
};

// 文件超出限制时的处理
const handleExceed = () => {
  ElMessage.warning('上传文档数量不超过20个');
};

// 重构文件验证逻辑，提高可读性
const handleChange = (file: UploadFile, files: UploadFile[]) => {
  // 过滤并验证文件
  const validFiles = files.filter((file: UploadFile) => {
    const rawFile = file.raw as File;
    const type = rawFile.type;
    const sizeMB = rawFile.size / 1024 / 1024;
    const name = rawFile.name.toLowerCase();

    // 优化：使用更清晰的文件类型判断
    const isExcel = type === 'application/vnd.ms-excel' || type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    const isCSV = type === 'text/csv' || type === 'application/csv' || type === 'text/x-comma-separated-values';
    const isJSONL = name.endsWith('.jsonl');

    const isValidType = isExcel || isCSV || isJSONL;
    const isSizeValid = sizeMB <= 100;

    if (!isValidType) {
      ElMessage.error(`文件 ${rawFile.name} 类型不支持，仅支持 .xlsx, .xls, .csv, .jsonl`);
      return false;
    }
    if (!isSizeValid) {
      ElMessage.error(`文件 ${rawFile.name} 大小不能超过 100MB`);
      return false;
    }
    return true;
  });

  fileList.value = validFiles.map(item => toRaw(item));
};

// 打开弹窗
const initData = (type: string, row?: any) => {
  formRef.value?.resetFields();
  dialogType.value = type;

  if (type === 'add') {
    form.value = {
      name: '',
      description: '',
      importMethod: '1',
    };
    fileList.value = [];
    parseModeList.value.forEach(item => (item.checked = false));
  } else if (type === 'addFeedback') {
    form.value = {
      id: row.id,
      knowledgeBaseId: row.knowledgeBaseId,
      name: row.name,
      description: row.description,
      importMethod: '1',
    };
    fileList.value = [];
    parseModeList.value.forEach(item => (item.checked = false));
  }
};

// 取消操作
const handleCancel = () => {
  knowStore.clearKnowParamsForm();
  router.replace({name: 'knowBase'});
};

const handleSubmit = async () => {
  try {
    const formValid = await formRef.value?.validate();
    if (!formValid) return;

    if (fileList.value?.length === 0) {
      ElMessage.warning('请先选择文件');
      return;
    }

    if (dialogType.value === 'add') {
      // 创建知识库
      const formData = new FormData();
      formData.append('name', form.value.name);
      formData.append('description', form.value.description);

      // 处理复选框值
      const analysisStrategy = [...parseModeList.value.map((item, index) => (item.checked ? index + 1 : null))].filter(Boolean).join(',');
      formData.append('innerDocumentsSaveReqVO.analysisStrategy', analysisStrategy);
      formData.append('innerDocumentsSaveReqVO.innerDocumentsSaveReqVO', form.value.importMethod);

      // 添加文件到 FormData
      fileList.value.forEach((file: UploadFile) => {
        if (file.raw) {
          formData.append('innerDocumentsSaveReqVO.files', file.raw);
        }
      });

      await api.knowledgeCreate(formData);
    } else if (dialogType.value === 'addFeedback') {
      // 上传知识库文档
      const formData = new FormData();
      // formData.append('name', form.value.name);
      formData.append('knowledgeId', form.value.id);
      formData.append('knowledgeBaseId', form.value.knowledgeBaseId);
      // formData.append('description', form.value.description);

      // 处理复选框值
      const analysisStrategy = [...parseModeList.value.map((item, index) => (item.checked ? index + 1 : null))].filter(Boolean).join(',');
      formData.append('analysisStrategy', analysisStrategy);
      formData.append('importMethod', form.value.importMethod);
      // 添加文件到 FormData
      fileList.value.forEach((file: any) => {
        if (file.raw) {
          console.log('[ file ] >', file);
          formData.append('files', file.raw);
        }
      });
      await api.documentsUpload(formData);
    }
    // else {
    //   // 更新知识库
    //   const updateData = {
    //     id: form.value.id,
    //     name: form.value.name,
    //     description: form.value.description,
    //     knowledgeBaseId: form.value.knowledgeBaseId,
    //   };
    //   await api.knowledgeUpdate(updateData);
    // }

    ElMessage.success('提交成功');
    handleCancel();
  } catch (error: any) {
    console.error('表单提交错误:', error);
  }
};
</script>

<style scoped lang="scss">
.el-dialog {
  .el-dialog__header {
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__title {
    font-size: 16px;
    font-weight: bold;
  }

  .el-dialog__headerbtn {
    top: 10px;
  }
}

.el-form-item {
  margin-bottom: 20px;

  .el-form-item__label {
    font-weight: normal;
    padding-bottom: 8px;
    color: #606266;
  }

  .el-form-item__content {
    line-height: 1.5;
  }

  .el-form-item__error {
    margin-top: 5px;
  }
}

.el-textarea__inner {
  min-height: 100px !important;
}

.upload-demo {
  p {
    color: #999;
  }
}

.import-type-prompt {
  width: fit-content;
  padding: 1px 28px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.12);
  border-radius: 10px;
}

.import-type-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-top: 15px;

  .import-type {
    width: 48%;
    padding: 10px;
    border: 1px solid #999;
    border-radius: 10px;
    transition: all 0.3s;

    &.import-type-active {
      border-color: #409eff;
      background-color: rgba(64, 158, 255, 0.05);
    }

    .import-type-title {
      font-size: 16px;
      font-weight: bold;
    }

    .import-type-description {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }
  }
}

:deep(.el-upload-dragger) {
  padding: 25px 10px;
}

.upload-demo-tip {
  font-size: 16px;
  font-weight: bold;
}

.el-row {
  margin-bottom: 8px;
  &.second-row {
    margin-bottom: 0;
  }
}

.el-col {
  margin-bottom: 8px;
}

// 复选框 + 描述样式
.el-checkbox {
  display: inline-flex;
  align-items: center;

  .desc {
    margin-left: 4px;
    font-size: 12px;
    color: #666;
  }
}

:deep(.el-upload-list) {
  max-height: 130px;
  overflow-y: auto;
}
</style>
