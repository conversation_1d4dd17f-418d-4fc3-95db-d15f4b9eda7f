<template>
  <el-dialog v-model="dialogVisible" title="创建名词" width="30%" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" size="default">
      <el-form-item label="分组名称" prop="groupName">
        <el-input v-model="form.groupName" placeholder="请输入分组名称"></el-input>
      </el-form-item>

      <el-form-item label="权限分配">
        <div class="permission-tabs">
          <el-button v-for="(tab, index) in permissionTabs" :key="index" :type="activeTab === tab.value ? 'primary' : ''" @click="activeTab = tab.value" style="margin-right: 5px">
            {{ tab.label }}
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <el-input v-model="searchQuery" :prefix-icon="Search" placeholder="搜索用户" class="search-input"></el-input>
    <el-table :data="tableData" style="width: 100%; margin-top: 15px">
      <el-table-column prop="name" label="用户" width="200"></el-table-column>
      <el-table-column label="编辑">
        <template #default="scope">
          <el-radio-group v-model="scope.row.permission">
            <el-radio :value="'edit'"></el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
      <el-table-column label="使用">
        <template #default="scope">
          <el-radio-group v-model="scope.row.permission">
            <el-radio :value="'use'"></el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {ref, reactive} from 'vue';
import {ElMessage} from 'element-plus';
import {Search} from '@element-plus/icons-vue';
import * as api from '@/api/know/noun/index';

const formRef = ref<any>(null);
const dialogVisible = ref<boolean>(false);
const emit = defineEmits(['getGroupData']);

const form = reactive({
  groupName: '',
});

const rules = reactive({
  groupName: [{required: true, message: '请输入分组名称', trigger: 'blur'}],
});

const activeTab = ref<string>('user');
const searchQuery = ref<string>('');
const tableData = ref<any[]>([]);

const permissionTabs = [
  {label: '按用户', value: 'user'},
  {label: '按用户组', value: 'group'},
];

const handleClose = () => {
  dialogVisible.value = false;
};

const handleSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        await api.groupCreate({id: '', name: form.groupName, priority: '0'});
        ElMessage.success('提交成功');
        emit('getGroupData');
        dialogVisible.value = false;
      } catch (error: any) {
        console.error('提交失败:', error);
        ElMessage.error('提交失败，请重试');
      }
    } else {
      ElMessage.error('请填写完整信息');
    }
  });
};

const resetForm = () => {
  formRef.value?.resetFields();
  form.groupName = '';
  tableData.value = tableData.value.map(item => ({
    ...item,
    permission: 'use',
  }));
};

const openDialog = () => {
  dialogVisible.value = true;
  resetForm();
};

defineExpose({openDialog});
</script>

<style scoped lang="scss">
.permission-tabs {
  display: flex;
  margin-bottom: 15px;
}

.search-input {
  width: 100%;
  margin: auto;
  margin-bottom: 15px;
}

.radio-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
