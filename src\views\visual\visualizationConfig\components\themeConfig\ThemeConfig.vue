<template>
  <el-collapse v-model="activeNames" class="theme-config-collapse">
    <el-collapse-item title="基础信息" name="base">
      <ThemeBaseInfo />
    </el-collapse-item>
    <el-collapse-item title="全局样式" name="style">
      <ThemeGlobalStyle />
    </el-collapse-item>
    <el-collapse-item title="页面布局" name="layout">
      <ThemeLayout />
    </el-collapse-item>
  </el-collapse>
</template>

<script setup>
import {ref} from 'vue';
import ThemeBaseInfo from './components/ThemeBaseInfo.vue';
import ThemeGlobalStyle from './components/ThemeGlobalStyle.vue';
import ThemeLayout from './components/ThemeLayout.vue';
const activeNames = ref(['base', 'style', 'layout']);
</script>

<style scoped lang="scss">
.theme-config-collapse {
  background: #fff;
  border: none;
  :deep(.el-collapse-item__header) {
    font-weight: bold;
    font-size: 18px;
  }
}
</style>
