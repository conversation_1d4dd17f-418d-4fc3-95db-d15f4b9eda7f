<template>
  <div>
    <el-dialog v-model="dialogVisible" title="权限分配" width="300" :show-close="false">
      <template #header="{close, titleId, titleClass}">
        <div class="my-header">
          <span :id="titleId" :class="titleClass">权限分配</span>
          <el-icon style="width: 18px; height: 18px" @click="close"><svg-icon name="close" /></el-icon>
        </div>
      </template>
      <div class="permission-tab">
        <div
          class="permission-tab-item"
          v-for="item in roleUser"
          :key="item.name"
          :style="rolePermission == item.value ? {backgroundColor: '#fff', color: '#4361EE'} : {}"
          :type="rolePermission == item.value ? 'primary' : ''"
          @click="permissionClick(item)"
        >
          {{ item.name }}
        </div>
      </div>
      <el-input v-model="searchValue" style="width: 100%; margin-top: 10px" :suffix-icon="Search" />
      <div style="width: 100%; padding: 10px; border-radius: 8px; border: 1px solid #eee; background: #f5f7fa; margin-top: 10px">
        <GeneralTables :columns="columns" :data="tableData" :border="false" :stripe="true" :pagination="false" :header-cell-style="headerCellStyle" style="width: 100%">
          <template #column-edit="scope">
            <el-radio value="1" v-model="scope.row.edit" @change="() => handleRadioChange(scope.row, 'edit')"></el-radio>
          </template>
          <template #column-use="scope">
            <el-radio value="2" v-model="scope.row.use" @change="() => handleRadioChange(scope.row, 'use')"></el-radio>
          </template>
        </GeneralTables>
      </div>

      <template #footer>
        <div class="dialog-footer" style="display: flex; justify-content: center">
          <el-button type="primary" @click="save">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue';
import {Search} from '@element-plus/icons-vue';
import SvgIcon from '@/components/SvgIcon.vue';
import GeneralTables from '@/components/GeneralTables/index.vue';

let dialogVisible = ref(false);
let roleUser = ref([
  {
    name: '用户',
    value: '1',
  },
  {
    name: '组织',
    value: '2',
  },
  {
    name: '角色',
    value: '3',
  },
]);
let rolePermission = ref('1');
let searchValue = ref('');
let tableData = computed(() => {
  return permissionList.value.find(item => item.id === rolePermission.value).tableData;
});
let columns = computed(() => {
  return permissionList.value.find(item => item.id == rolePermission.value).columns;
});
let permissionList = ref([
  {
    id: '1',
    tableData: [
      {
        user: '张晓芳',
        edit: '1',
        use: false,
      },
    ],
    columns: [
      {
        prop: 'user',
        label: '用户',
      },
      {
        prop: 'edit',
        label: '编辑',
        slot: true,
      },
      {
        prop: 'use',
        label: '使用',
        slot: true,
      },
    ],
  },
  {
    id: '2',
    tableData: [
      {
        user: '张痔尾',
        edit: false,
        use: '2',
      },
    ],
    columns: [
      {
        prop: 'user',
        label: '组织',
      },
      {
        prop: 'edit',
        label: '编辑',
        slot: false,
      },
      {
        prop: 'use',
        label: '使用',
        slot: true,
      },
    ],
  },
  {
    id: '3',
    tableData: [
      {
        user: '张紫薇',
        edit: false,
        use: '2',
      },
    ],
    columns: [
      {
        prop: 'user',
        label: '角色',
      },
      {
        prop: 'edit',
        label: '编辑',
        slot: false,
      },
      {
        prop: 'use',
        label: '使用',
        slot: true,
      },
    ],
  },
]);

onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
});

const handleRadioChange = (row, type) => {
  // 如果选中了一个，就取消另一个
  if (type === 'edit') {
    row.use = false;
  } else if (type === 'use') {
    row.edit = false;
  }
  console.log(tableData.value);
};
const openDialog = () => {
  dialogVisible.value = true;
};
const permissionClick = item => {
  console.log(item);

  rolePermission.value = item.value;
};
// 表头样式
const headerCellStyle = {
  backgroundColor: '#fff',
  color: '#303133',
  padding: '8px 0',
};
const save = () => {
  console.log(permissionList.value);

  dialogVisible.value = false;
};
// 使用toRefs解构
// let { } = { ...toRefs(data) }
defineExpose({openDialog});
</script>
<style scoped lang="scss">
.my-header {
  display: flex;
  justify-content: space-between;
}

.permission-tab {
  display: flex;
  width: fit-content;
  padding: 4px;
  background-color: #f5f7fa;
  border-radius: 10px;
  margin: auto;
  .permission-tab-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 48px;
    height: 26px;
    border-radius: 6px;
    color: #999;
    cursor: pointer;
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #fff !important;
    }
  }
}
</style>
