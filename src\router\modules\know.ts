import type {RouteRecordRaw} from 'vue-router';

export const knowRoutes: RouteRecordRaw[] = [
  {
    path: '/know',
    meta: {
      title: '知识管理',
      grouping: true,
      menu: true,
      icon: 'menu/knowManage',
    },
    children: [
      {
        path: 'knowbase',
        name: 'knowBase',
        component: () => import('@/views/know/knowPage/knowbase/index.vue'),
        meta: {
          title: '知识库',
          menu: true,
          icon: 'menu/knowBase',
        },
      },
      {
        path: 'knowdetail',
        name: 'KnowDetail',
        component: () => import('@/views/know/knowPage/knowDetail/index.vue'),
        meta: {
          title: '知识文档',
          menu: false,
          isActive: '/know/knowbase',
        },
      },
      {
        path: 'createknowbase',
        name: 'CreateKnowBase',
        component: () => import('@/views/know/knowPage/createKnowBase/index.vue'),
        meta: {
          title: '创建知识库',
          menu: false,
          isActive: '/know/knowbase',
        },
      },
      {
        path: 'hittesting',
        name: 'HitTesting',
        component: () => import('@/views/know/knowPage/hitTesting/index.vue'),
        meta: {
          title: '命中测试',
          menu: false,
          isActive: '/know/knowbase',
        },
      },
      {
        path: 'slicedetails',
        name: 'sliceDetails',
        component: () => import('@/views/know/knowPage/sliceDetails/index.vue'),
        meta: {
          title: '查看切片',
          menu: false,
          isActive: '/know/knowbase',
        },
      },
      {
        path: 'noun',
        name: 'Noun',
        component: () => import('@/views/know/nounPage/noun/index.vue'),
        meta: {
          title: '名词定义',
          menu: true,
          icon: 'menu/definition',
        },
      },
      {
        path: 'addpriority',
        name: 'AddPriority',
        component: () => import('@/views/know/nounPage/addPriority/index.vue'),
        meta: {
          title: '添加名词定义',
          menu: false,
          isActive: '/know/noun',
        },
      },
      {
        path: 'chart',
        name: 'Chart',
        component: () => import('@/views/know/graphPage/index.vue'),
        meta: {
          title: '图谱',
          menu: true,
          isActive: '/know/chart',
          icon: 'menu/graph',
        },
      },
    ],
  },
];
