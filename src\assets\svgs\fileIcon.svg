<svg width="48" height="44" viewBox="0 0 48 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="48" height="44" fill="#1E1E1E"/>
<rect width="5601" height="13000" transform="translate(-3420 -5872)" fill="#222222"/>
<g clip-path="url(#clip0_11_2850)">
<rect width="1920" height="1080" transform="translate(-1096 -475)" fill="#F5F7FA"/>
<g filter="url(#filter0_dd_11_2850)">
<rect x="-436" y="-385" width="1250" height="980" rx="10" fill="white"/>
<mask id="path-2-inside-1_11_2850" fill="white">
<path d="M-117 -43H199V7H-117V-43Z"/>
</mask>
<path d="M-117 -43H199V7H-117V-43Z" fill="#F5F8FA"/>
<path d="M199 7V8H200V7H199ZM199 -43H198V7H199H200V-43H199ZM199 7V6H-117V7V8H199V7Z" fill="#E6EBF0" mask="url(#path-2-inside-1_11_2850)"/>
</g>
<rect x="-1096" y="-475" width="1920" height="1080" fill="black" fill-opacity="0.5"/>
<g filter="url(#filter1_dd_11_2850)">
<path d="M-1096 -385C-1096 -390.523 -1091.52 -395 -1086 -395H814C819.523 -395 824 -390.523 824 -385V605H-1096V-385Z" fill="#F5F7FA"/>
<rect x="-755.5" y="-310.5" width="1559" height="895" rx="9.5" fill="white"/>
<rect x="-755.5" y="-310.5" width="1559" height="895" rx="9.5" stroke="#EEEEEE"/>
<rect x="-426" y="-39" width="900" height="179" rx="8" fill="#2F51FF" fill-opacity="0.08" stroke="#2F51FF" stroke-opacity="0.2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="4 4"/>
<path d="M4.79994 33.448C4.79115 34.548 5.21963 35.6064 5.99113 36.3904C6.76264 37.1745 7.81398 37.62 8.91394 37.629H16.6229C17.2178 37.6279 17.8054 37.497 18.3444 37.2453C18.8835 36.9937 19.3611 36.6274 19.7439 36.172L21.7439 33.801C22.1268 33.3456 22.6044 32.9793 23.1435 32.7277C23.6825 32.476 24.27 32.3451 24.8649 32.344H39.7649C40.8649 32.335 41.9162 31.8895 42.6877 31.1054C43.4592 30.3214 43.8877 29.263 43.8789 28.163V4.181C43.8877 3.08104 43.4592 2.02264 42.6877 1.23857C41.9162 0.454499 40.8649 0.00898122 39.7649 0H8.91394C7.81398 0.00898122 6.76264 0.454499 5.99113 1.23857C5.21963 2.02264 4.79115 3.08104 4.79994 4.181V33.448Z" fill="#2F51FF"/>
<path d="M0.000161008 10.571C-0.0110212 9.10478 0.560663 7.69416 1.58947 6.64941C2.61827 5.60466 4.01994 5.01136 5.48616 5L14.6002 5C15.3149 5.00107 16.0224 5.14292 16.6823 5.41745C17.3422 5.69198 17.9416 6.09381 18.4462 6.6L20.7302 8.88C21.2348 9.38619 21.8341 9.78802 22.494 10.0625C23.1539 10.3371 23.8614 10.4789 24.5762 10.48H42.5142C43.9799 10.4914 45.3811 11.0843 46.4098 12.1283C47.4385 13.1724 48.0105 14.5823 48.0002 16.048V38.428C48.0058 39.1541 47.8684 39.8742 47.5958 40.5472C47.3232 41.2201 46.9206 41.8328 46.4112 42.3502C45.9018 42.8676 45.2954 43.2797 44.6268 43.5627C43.9581 43.8458 43.2402 43.9944 42.5142 44H5.48616C4.01994 43.9886 2.61827 43.3953 1.58947 42.3506C0.560663 41.3058 -0.0110212 39.8952 0.000161008 38.429V10.571Z" fill="url(#paint0_linear_11_2850)"/>
<path d="M18.192 27.8971C17.9309 27.8973 17.6756 27.8209 17.4575 27.6774C17.2395 27.5339 17.0683 27.3295 16.9653 27.0897C16.8622 26.8498 16.8318 26.585 16.8778 26.3281C16.9238 26.0711 17.0441 25.8333 17.224 25.6441L23.291 19.2501C23.4191 19.1149 23.5734 19.0073 23.7444 18.9337C23.9155 18.8602 24.0998 18.8223 24.286 18.8223C24.4722 18.8223 24.6565 18.8602 24.8275 18.9337C24.9986 19.0073 25.1529 19.1149 25.281 19.2501L31.348 25.6431C31.5278 25.8323 31.6482 26.0701 31.6942 26.3271C31.7402 26.584 31.7097 26.8488 31.6067 27.0887C31.5036 27.3285 31.3325 27.5329 31.1144 27.6764C30.8964 27.8199 30.641 27.8963 30.38 27.8961H27.96C27.778 27.8961 27.6036 27.9683 27.4749 28.097C27.3463 28.2256 27.274 28.4001 27.274 28.5821V31.2301C27.2741 31.4102 27.2387 31.5886 27.1699 31.755C27.1011 31.9215 27.0001 32.0727 26.8728 32.2002C26.7455 32.3276 26.5943 32.4287 26.4279 32.4976C26.2615 32.5666 26.0831 32.6021 25.903 32.6021H22.669C22.3054 32.6021 21.9567 32.4576 21.6995 32.2005C21.4424 31.9434 21.298 31.5947 21.298 31.2311V28.5821C21.298 28.4001 21.2257 28.2256 21.0971 28.097C20.9684 27.9683 20.7939 27.8961 20.612 27.8961L18.192 27.8971Z" fill="white" fill-opacity="0.9"/>
</g>
</g>
<defs>
<filter id="filter0_dd_11_2850" x="-442" y="-391" width="1266" height="996" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.32 0 0 0 0 0.352 0 0 0 0 0.4 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11_2850"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.32 0 0 0 0 0.352 0 0 0 0 0.4 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_11_2850" result="effect2_dropShadow_11_2850"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_11_2850" result="shape"/>
</filter>
<filter id="filter1_dd_11_2850" x="-1102" y="-401" width="1936" height="1016" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.32 0 0 0 0 0.352 0 0 0 0 0.4 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11_2850"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.32 0 0 0 0 0.352 0 0 0 0 0.4 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_11_2850" result="effect2_dropShadow_11_2850"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_11_2850" result="shape"/>
</filter>
<linearGradient id="paint0_linear_11_2850" x1="-3.84002" y1="-15.475" x2="17.3843" y2="56.0265" gradientUnits="userSpaceOnUse">
<stop stop-color="#8FC6FE"/>
<stop offset="1" stop-color="#2F51FF"/>
</linearGradient>
<clipPath id="clip0_11_2850">
<rect width="1920" height="1080" fill="white" transform="translate(-1096 -475)"/>
</clipPath>
</defs>
</svg>
