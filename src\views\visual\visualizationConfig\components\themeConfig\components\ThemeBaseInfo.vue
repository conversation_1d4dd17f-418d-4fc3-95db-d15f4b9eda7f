<template>
  <div>
    <div class="form-row">
      <label class="form-label">主题名称</label>
      <el-input class="form-input" placeholder="请输入主题名称" v-model="visualStore.pageConfig.basicConfig.themeTitle" />
    </div>
    <div class="form-row">
      <label class="form-label">主题封面</label>
      <el-popover placement="top" :width="365" trigger="click" title="主题封面">
        <template #default>
          <div class="upload-container">
            <el-upload class="upload-demo" drag action="#" :show-file-list="false" :limit="1" :auto-upload="false" :file-list="fileList" @change="handleFileChange" accept=".jpg,.jpeg,.png,.gif,.svg">
              <el-button type="primary" plain :icon="Upload">上传本地图片</el-button>
              <div class="upload-tip">只支持 jpg, jpeg, png, gif, svg 格式，最大 1M</div>
            </el-upload>

            <div class="upload-link-row">
              <el-input v-model="customImgUrl" placeholder="请输入图片网址" class="upload-link-input" />
              <el-button type="primary" @click="useCustomImg" :disabled="!customImgUrl">使用</el-button>
            </div>

            <div class="image-popover-footer">
              <el-button type="primary" link @click="visualStore.pageConfig.basicConfig.themeCover = ''">清空图片</el-button>
            </div>
          </div>
        </template>

        <template #reference>
          <div class="image-upload-placeholder" v-if="visualStore.pageConfig.basicConfig.themeCover === ''">
            <span class="plus">+</span>
          </div>
          <img v-else class="image-upload-placeholder-img" :src="visualStore.pageConfig.basicConfig.themeCover" alt="" />
        </template>
      </el-popover>
    </div>

    <div class="form-row">
      <label class="form-label">标题字体</label>
      <div class="text-style-toolbar">
        <!-- 字体选择 -->
        <el-select
          v-model="currentFontFamily"
          class="font-select"
          placeholder="选择字体"
          size="small"
          @change="handleFontFamilyChange"
        >
          <el-option label="Arial" value="Arial" />
          <el-option label="Times New Roman" value="Times New Roman" />
          <el-option label="Microsoft YaHei" value="Microsoft YaHei" />
          <el-option label="SimSun" value="SimSun" />
          <el-option label="SimHei" value="SimHei" />
        </el-select>

        <!-- 字号选择 -->
        <el-input-number
          v-model="currentFontSize"
          class="font-size-input"
          :min="8"
          :max="72"
          size="small"
          controls-position="right"
        />

        <!-- 样式按钮 -->
        <div class="style-buttons">
          <el-button
            :type="isBold ? 'primary' : 'default'"
            size="small"
            class="style-btn bold-btn"
            @click="toggleBold"
            title="加粗"
          >
            <strong>B</strong>
          </el-button>

          <el-button
            :type="isItalic ? 'primary' : 'default'"
            size="small"
            class="style-btn italic-btn"
            @click="toggleItalic"
            title="斜体"
          >
            <em>I</em>
          </el-button>
        </div>

        <!-- 对齐方式 -->
        <div class="align-buttons">
          <el-button
            :type="titleAlign === 'left' ? 'primary' : 'default'"
            size="small"
            class="align-btn"
            @click="setAlign('left')"
            title="左对齐"
          >
            <svg class="icon" viewBox="0 0 1024 1024">
              <path d="M128 192h768v64H128zM128 384h512v64H128zM128 576h768v64H128zM128 768h512v64H128z"/>
            </svg>
          </el-button>

          <el-button
            :type="titleAlign === 'center' ? 'primary' : 'default'"
            size="small"
            class="align-btn"
            @click="setAlign('center')"
            title="居中对齐"
          >
            <svg class="icon" viewBox="0 0 1024 1024">
              <path d="M128 192h768v64H128zM256 384h512v64H256zM128 576h768v64H128zM256 768h512v64H256z"/>
            </svg>
          </el-button>

          <el-button
            :type="titleAlign === 'right' ? 'primary' : 'default'"
            size="small"
            class="align-btn"
            @click="setAlign('right')"
            title="右对齐"
          >
            <svg class="icon" viewBox="0 0 1024 1024">
              <path d="M128 192h768v64H128zM384 384h512v64H384zM128 576h768v64H128zM384 768h512v64H384z"/>
            </svg>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed} from 'vue';
import {useVisualStore} from '@/store/modules/visual';
import {Upload} from '@element-plus/icons-vue';
import {ElMessage} from 'element-plus';
import { uploadImage, getImageUrl } from '@/api/visualTheme';

const visualStore = useVisualStore();

const customImgUrl = ref('');
const fileList = ref([]);

// 确保 titleFont 对象存在
const ensureTitleFont = () => {
  if (!visualStore.pageConfig.basicConfig.titleFont) {
    visualStore.pageConfig.basicConfig.titleFont = {
      fontFamily: "Arial",
      fontSize: 16,
      fontWeight: "normal",
      fontStyle: "normal",
      textAlign: "center"
    };
  }
};

// 当前字体系列的计算属性
const currentFontFamily = computed({
  get: () => {
    ensureTitleFont();
    return visualStore.pageConfig.basicConfig.titleFont.fontFamily || 'Arial';
  },
  set: (value) => {
    ensureTitleFont();
    visualStore.pageConfig.basicConfig.titleFont.fontFamily = value;
  }
});

// 当前字号的计算属性
const currentFontSize = computed({
  get: () => {
    ensureTitleFont();
    return visualStore.pageConfig.basicConfig.titleFont.fontSize || 16;
  },
  set: (value) => {
    ensureTitleFont();
    visualStore.pageConfig.basicConfig.titleFont.fontSize = value;
  }
});

// 是否加粗的计算属性
const isBold = computed(() => {
  ensureTitleFont();
  return visualStore.pageConfig.basicConfig.titleFont.fontWeight === 'bold' ||
         visualStore.pageConfig.basicConfig.titleFont.fontWeight === 700;
});

// 是否斜体的计算属性
const isItalic = computed(() => {
  ensureTitleFont();
  return visualStore.pageConfig.basicConfig.titleFont.fontStyle === 'italic';
});

// 标题对齐方式的计算属性
const titleAlign = computed({
  get: () => {
    ensureTitleFont();
    return visualStore.pageConfig.basicConfig.titleFont.textAlign || 'center';
  },
  set: (value) => {
    ensureTitleFont();
    visualStore.pageConfig.basicConfig.titleFont.textAlign = value;
  }
});

/**
 * 处理本地图片上传
 * @param {Object} file - 上传的文件对象
 */
async function handleFileChange(file) {
  const raw = file.raw || file;

  // 文件类型和大小判断
  const isImage = /\.(jpg|jpeg|png|gif|svg)$/i.test(raw.name);
  const isLt1M = raw.size / 1024 / 1024 < 1;

  if (!isImage) {
    ElMessage.error('只支持 jpg、jpeg、png、gif、svg 格式');
    return;
  }
  if (!isLt1M) {
    ElMessage.error('图片大小不能超过 1M');
    return;
  }

  try {
    // 调用上传接口
    const response = await uploadImage(raw);

    console.log('主题封面上传响应:', response);

    if (response.code === 200) {
      // 使用文件名生成访问地址
      const imageUrl = getImageUrl(response.data.fileName);
      console.log('生成的主题封面URL:', imageUrl);
      visualStore.pageConfig.basicConfig.themeCover = imageUrl;
      ElMessage.success('图片上传成功');
    } else {
      ElMessage.error(response.message || '上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error('上传失败，请重试');
  }

  // 清空 fileList 防止重复不触发
  fileList.value = [];
}

/**
 * 使用外链图片
 */
function useCustomImg() {
  visualStore.pageConfig.basicConfig.themeCover = customImgUrl.value;
  ElMessage.success('已使用图片链接');
}

// 处理字体系列变化
const handleFontFamilyChange = (value) => {
  ensureTitleFont();
  visualStore.pageConfig.basicConfig.titleFont.fontFamily = value;
};

// 切换加粗
const toggleBold = () => {
  ensureTitleFont();
  if (visualStore.pageConfig.basicConfig.titleFont.fontWeight === 'bold' ||
      visualStore.pageConfig.basicConfig.titleFont.fontWeight === 700) {
    visualStore.pageConfig.basicConfig.titleFont.fontWeight = 'normal';
  } else {
    visualStore.pageConfig.basicConfig.titleFont.fontWeight = 'bold';
  }
};

// 切换斜体
const toggleItalic = () => {
  ensureTitleFont();
  if (visualStore.pageConfig.basicConfig.titleFont.fontStyle === 'italic') {
    visualStore.pageConfig.basicConfig.titleFont.fontStyle = 'normal';
  } else {
    visualStore.pageConfig.basicConfig.titleFont.fontStyle = 'italic';
  }
};

// 设置对齐方式
const setAlign = (align) => {
  ensureTitleFont();
  visualStore.pageConfig.basicConfig.titleFont.textAlign = align;
};
</script>

<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18px;

  .form-label {
    width: 80px;
    color: #222;
    font-size: 14px;
    flex-shrink: 0;
    line-height: 32px; // 与输入框高度对齐
  }

  .form-input {
    flex: 1;
    min-width: 0;
  }

  .image-upload-placeholder,
  .image-upload-placeholder-img {
    width: 100px;
    height: 70px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ddd;
    margin-left: 8px;

    .plus {
      font-size: 32px;
      color: #666;
      font-weight: bold;
    }
  }

  .image-upload-placeholder-img {
    object-fit: cover;
    background: none;
  }
}

// 上传模块
.upload-demo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  :deep(.el-upload-dragger) {
    border: none;
    padding: 0;
    text-align: left;
  }
}

.upload-tip {
  text-align: left;
  color: #888;
  font-size: 13px;
  margin-top: 15px;
}

.upload-link-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;

  .upload-link-input {
    flex: 1;
  }
}

// Popover 底部清空按钮
.image-popover-footer {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ccc;
  text-align: right;
}

// 标题字体工具栏样式
.text-style-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;

  .font-select {
    width: 140px;
  }

  .font-size-input {
    width: 80px;

    :deep(.el-input__inner) {
      text-align: center;
    }
  }

  .style-buttons {
    display: flex;
    gap: 4px;

    .style-btn {
      width: 32px;
      height: 32px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      &.bold-btn strong {
        font-size: 14px;
        font-weight: 700;
      }

      &.italic-btn em {
        font-size: 14px;
        font-style: italic;
      }
    }
  }

  .align-buttons {
    display: flex;
    gap: 4px;
    margin-left: 8px;

    .align-btn {
      width: 32px;
      height: 32px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
    }
  }
}

.icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}
</style>
