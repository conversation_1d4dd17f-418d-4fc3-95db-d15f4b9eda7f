.content-card {
  position: relative;
  height: calc(100% - 100px);
  width: calc(100%);
  margin: auto;
  .el-card__body {
    height: 100%;
  }
}
.header-back-icon {
  cursor: pointer;
  font-size: 20px;
  margin-right: 10px;
}
.content-header {
  display: flex;
  justify-content: space-between;

  .search-input,
  .testing-input {
    width: 200px;

    :deep(.el-input__inner) {
      border-radius: 20px;
    }
  }
}
.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  .content-footer {
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
}
