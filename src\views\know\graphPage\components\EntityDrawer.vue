<template>
  <el-drawer v-model="showDrawer" :with-header="false" size="35%">
    <div class="drawer-header">
      <span class="drawer-header-title">{{ drawerType === 'add' ? '新增' : '编辑' }}实体类型</span>
      <div>
        <el-button type="danger" @click="deleteNode" v-show="drawerType !== 'add'">删除节点</el-button>
        <el-button type="primary" @click="editTheEntity" v-show="drawerType === 'details'">编辑实体</el-button>
        <el-button @click="handleClose" v-show="drawerType === 'add' || drawerType === 'edit'">取消</el-button>
        <el-button type="primary" @click="handleSave" v-show="drawerType === 'add' || drawerType === 'edit'">保存</el-button>
      </div>
    </div>
    <el-form ref="formRef" :model="formData" :rules="drawerType === 'add' || drawerType === 'edit' ? rules : {}" label-width="80px">
      <!-- 实体基本信息 -->
      <el-form-item label="实体名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入实体名称" v-show="drawerType === 'add' || drawerType === 'edit'" />
        <span v-show="drawerType === 'details'">{{ formData.name }}</span>
      </el-form-item>
      <el-form-item label="实体类型" prop="typeId">
        <el-select v-model="formData.typeId" placeholder="请选择实体类型" v-show="drawerType === 'add' || drawerType === 'edit'">
          <el-option v-for="item in entityTypes" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <span v-show="drawerType === 'details'">{{ entityTypes.find(item => item.id === formData.typeId)?.name }}</span>
      </el-form-item>
      <el-form-item label="实体描述" prop="desc">
        <el-input v-model="formData.desc" type="textarea" placeholder="请输入实体描述" v-show="drawerType === 'add' || drawerType === 'edit'" />
        <span v-show="drawerType === 'details'">{{ formData.desc }}</span>
      </el-form-item>

      <!-- 关系列表 -->
      <div class="relation-title" v-show="drawerType === 'details' || drawerType === 'edit'">
        <span>关系</span>
        <el-button size="small" @click="openAddRelationDialog">添加关系</el-button>
      </div>
      <div class="relation-container" v-show="drawerType === 'details' || drawerType === 'edit'">
        <el-table :data="relations" style="width: 100%">
          <el-table-column prop="typeName" label="关系" />
          <el-table-column prop="nodeName" label="实体名称" />
          <el-table-column prop="nodeTypeName" label="实体类型">
            <template #default="scope">
              <el-tag type="info" :style="{background: getTagType(scope.row.nodeTypeName), color: '#fff'}" disable-transitions>{{ scope.row.nodeTypeName }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="danger" link @click="handleDelete(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 添加关系弹窗组件 -->
      <AddRelationDialog ref="addRelationDialogRef" />
    </el-form>
  </el-drawer>
</template>

<script setup lang="ts">
import {ref, reactive, defineEmits} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import AddRelationDialog from './AddRelationDialog.vue';
import * as api from '@/api/know/graph/index';

const emits = defineEmits(['addARelationship', 'getKnowledgeData']);

const drawerType = ref('');
const showDrawer = ref(false);

// 事件定义
const formRef = ref();
const formData = reactive<any>({
  id: '',
  name: '',
  typeId: '',
  desc: '',
});

const relations = ref([]);

const rules = reactive({
  name: [{required: true, message: '请输入实体名称', trigger: 'blur'}],
  typeId: [{required: true, message: '请选择实体类型', trigger: 'change'}],
  desc: [{required: true, message: '请输入实体描述', trigger: 'blur'}],
});

// 实体类型和关系类型选项
const entityTypes = ref([]);

// 添加关系弹窗状态和数据
const addRelationDialogRef = ref<InstanceType<typeof AddRelationDialog> | null>(null);

const getTagType = (type: any) => {
  if (type === '指标') return '#96F202';
  if (type === '维度') return '#155EFE';
  if (type === '统计周期') return '#f8b160';
  if (type === '指标集合') return '#81D3F8';
  return '#F4F4F5';
};

const getTagTypeText = (type: any) => {
  if (type === 1) return '指标';
  if (type === 2) return '维度';
  if (type === 3) return '统计周期';
  if (type === 4) return '指标集合';
  return '';
};

const getRelations = async () => {
  const res = await api.nodeRelationList(formData.id);
  relations.value = res.relationshipList;
};
// 获取实体类型列表
const getTagTypeTextList = async () => {
  try {
    const res = await api.graphNodePage();
    entityTypes.value = res.list;
  } catch (error) {}
};

// 关闭弹窗
const handleClose = () => {
  showDrawer.value = false;
};

// 保存表单
const handleSave = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    if (drawerType.value === 'add') {
      await api.graphCreateNode(formData);
      ElMessage.success('保存成功');
    } else {
      await api.graphUpdateNode(formData);
      ElMessage.success('更新成功');
    }
    handleClose();
    emits('getKnowledgeData');
  } catch (error) {
    console.log('表单验证失败:', error);
  }
};
const handleDelete = async (id: any) => {
  ElMessageBox.confirm(`确定要删除这条关系吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await api.graphDeleteRelationships([id]);
      ElMessage.success('删除成功');
      emits('getKnowledgeData');
      getRelations();
    })
    .catch(() => {});
};
// 打开添加关系弹窗
const openAddRelationDialog = () => {
  showDrawer.value = false;
  emits('addARelationship', formData.name);
};

const editTheEntity = () => {
  drawerType.value = 'edit';
};

const deleteNode = async () => {
  ElMessageBox.confirm(`确定要删除”${formData.name}“实体类型吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await api.graphDeleteNodes([formData.id]);
      ElMessage.success('删除成功');
      emits('getKnowledgeData');
    })
    .catch(() => {});
};

// 暴露打开方法
const open = async (type: string, item?: any) => {
  formRef.value?.clearValidate();
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  drawerType.value = type;
  if (type === 'edit' || type === 'details') {
    formData.id = item.id || '';
    await getTagTypeTextList();
    await getRelations();
    await getTagTypeTextList();
    formData.name = item.name || '';
    formData.desc = item.desc || '';
    formData.typeId = item.typeId || '';
  }
  showDrawer.value = true;
};
defineExpose({open});
</script>

<style scoped lang="scss">
.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20 20px 0px 20px;
  margin-bottom: 32px;
  .drawer-header-title {
    font-size: 16px;
    color: #76727b;
  }
}
.relation-title {
  display: flex;
  justify-content: space-between;
}
.relation-container {
  margin-top: 20px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
