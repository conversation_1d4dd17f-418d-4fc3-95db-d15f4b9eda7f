<template>
  <div class="form-row">
    <label class="form-label">配色设置</label>
    <div class="color-scheme-controls">
      <el-select
        @change="changeStyle"
        v-model="styleType"
        class="scheme-select"
        placeholder="请选择主题"
      >
        <el-option label="舒适" value="default" />
        <el-option
          v-for="(_, name) in currentCustomSchemes"
          :key="name"
          :label="name"
          :value="name"
        />
      </el-select>

      <el-button
        size="small"
        type="primary"
        @click="showAddSchemeDialog = true"
        :icon="Plus"
      >
        添加主题
      </el-button>

      <el-button
        v-if="canDeleteCurrentScheme"
        size="small"
        type="danger"
        @click="deleteCurrentScheme"
        :icon="Delete"
      >
        删除
      </el-button>
    </div>
  </div>
  <div class="form-row">
    <label class="form-label">颜色配置</label>
    <div class="color-picker-container">
      <el-color-picker
        v-for="(item, index) in styleColor"
        :key="`${styleType}-${index}`"
        size="small"
        v-model="item.color"
        @change="handleColorChange(item, index)"
        :disabled="!canEditCurrentScheme"
        class="color-picker-item"
      />
      <div v-if="!canEditCurrentScheme" class="readonly-tip">
        舒适主题不可修改
      </div>
    </div>
  </div>

  <!-- 添加主题对话框 -->
  <el-dialog
    v-model="showAddSchemeDialog"
    title="添加自定义主题"
    width="400px"
  >
    <el-form :model="newSchemeForm" label-width="80px">
      <el-form-item label="主题名称">
        <el-input
          v-model="newSchemeForm.name"
          placeholder="请输入主题名称"
          maxlength="20"
        />
      </el-form-item>
      <el-form-item label="复制自">
        <el-select v-model="newSchemeForm.copyFrom" placeholder="选择要复制的主题">
          <el-option label="舒适" value="default" />
          <el-option
            v-for="(_, name) in currentCustomSchemes"
            :key="name"
            :label="name"
            :value="name"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showAddSchemeDialog = false">取消</el-button>
      <el-button type="primary" @click="addNewScheme">确定</el-button>
    </template>
  </el-dialog>
  <!-- 柱状图特有配置 -->
  <div class="form-row" v-if="visualStore.chartType === 'barConfig'">
    <label class="form-label">柱体宽度</label>
    <el-slider
      v-model="barWidth"
      show-input
      size="small"
      @change="handleBarWidthChange"
    />
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'barConfig'">
    <label class="form-label">柱体圆角</label>
    <el-slider
      v-model="barBorderRadius"
      show-input
      size="small"
      @change="handleBarBorderRadiusChange"
    />
  </div>

  <!-- 条形图特有配置 -->
  <div class="form-row" v-if="visualStore.chartType === 'barHorizontalConfig'">
    <label class="form-label">柱体宽度</label>
    <el-slider
      v-model="barHorizontalWidth"
      :min="10"
      :max="50"
      show-input
      size="small"
      @change="handleBarHorizontalWidthChange"
    />
    <span class="unit">px</span>
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'barHorizontalConfig'">
    <label class="form-label">柱体圆角</label>
    <el-slider
      v-model="barHorizontalBorderRadius"
      :min="0"
      :max="20"
      show-input
      size="small"
      @change="handleBarHorizontalBorderRadiusChange"
    />
    <span class="unit">px</span>
  </div>

  <!-- 折线图特有配置 -->
  <div class="form-row" v-if="visualStore.chartType === 'lineConfig'">
    <label class="form-label">线条宽度</label>
    <el-slider
      v-model="lineWidth"
      :min="1"
      :max="10"
      show-input
      size="small"
      @change="handleLineWidthChange"
    />
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'lineConfig'">
    <label class="form-label">平滑曲线</label>
    <el-switch
      v-model="lineSmooth"
      @change="handleLineSmoothChange"
    />
  </div>

  <!-- 饼状图特有配置 -->
  <div class="form-row" v-if="visualStore.chartType === 'pieConfig'">
    <label class="form-label">内半径</label>
    <el-slider
      v-model="pieInnerRadius"
      :min="0"
      :max="80"
      show-input
      size="small"
      @change="handlePieInnerRadiusChange"
    />
    <span class="unit">%</span>
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'pieConfig'">
    <label class="form-label">外半径</label>
    <el-slider
      v-model="pieOuterRadius"
      :min="20"
      :max="100"
      show-input
      size="small"
      @change="handlePieOuterRadiusChange"
    />
    <span class="unit">%</span>
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'pieConfig'">
    <label class="form-label">显示标签</label>
    <el-switch
      v-model="pieShowLabel"
      @change="handlePieShowLabelChange"
    />
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'pieConfig'">
    <label class="form-label">显示引导线</label>
    <el-switch
      v-model="pieShowLabelLine"
      @change="handlePieShowLabelLineChange"
    />
  </div>

  <!-- 漏斗图特有配置 -->
  <div class="form-row" v-if="visualStore.chartType === 'funnelConfig'">
    <label class="form-label">排序方式</label>
    <el-select v-model="funnelSort" @change="handleFunnelSortChange" size="small">
      <el-option label="降序" value="descending" />
      <el-option label="升序" value="ascending" />
      <el-option label="无排序" value="none" />
    </el-select>
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'funnelConfig'">
    <label class="form-label">间隙大小</label>
    <el-slider
      v-model="funnelGap"
      :min="0"
      :max="10"
      show-input
      size="small"
      @change="handleFunnelGapChange"
    />
    <span class="unit">px</span>
  </div>

  <!-- 雷达图特有配置 -->
  <div class="form-row" v-if="visualStore.chartType === 'radarConfig'">
    <label class="form-label">雷达图半径</label>
    <el-slider
      v-model="radarRadius"
      :min="30"
      :max="90"
      show-input
      size="small"
      @change="handleRadarRadiusChange"
    />
    <span class="unit">%</span>
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'radarConfig'">
    <label class="form-label">显示分割区域</label>
    <el-switch
      v-model="radarShowSplitArea"
      @change="handleRadarShowSplitAreaChange"
    />
  </div>

  <!-- 仪表图特有配置 -->
  <div class="form-row" v-if="visualStore.chartType === 'gaugeConfig'">
    <label class="form-label">仪表盘半径</label>
    <el-slider
      v-model="gaugeRadius"
      :min="50"
      :max="90"
      show-input
      size="small"
      @change="handleGaugeRadiusChange"
    />
    <span class="unit">%</span>
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'gaugeConfig'">
    <label class="form-label">最小值</label>
    <el-input-number
      v-model="gaugeMin"
      :min="0"
      :max="500"
      size="small"
      @change="handleGaugeMinChange"
    />
  </div>
  <div class="form-row" v-if="visualStore.chartType === 'gaugeConfig'">
    <label class="form-label">最大值</label>
    <el-input-number
      v-model="gaugeMax"
      :min="500"
      :max="2000"
      size="small"
      @change="handleGaugeMaxChange"
    />
  </div>
</template>

<script setup>
import { onMounted, ref, computed, watch } from "vue";
import { useVisualStore } from "@/store/modules/visual";
import { Plus, Delete } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const visualStore = useVisualStore();

// 使用计算属性来获取当前图表的配色方案选择
const styleType = computed({
  get: () => {
    return visualStore.chartConfig[visualStore.chartType].selectedColorScheme || "default";
  },
  set: (value) => {
    visualStore.chartConfig[visualStore.chartType].selectedColorScheme = value;
  }
});

// 舒适主题（所有图表通用，不可修改）
const defaultColorScheme = [
  { color: "#409eff" },
  { color: "#f56c6c" },
  { color: "#e6a23c" },
  { color: "#5cb87a" },
  { color: "#909399" },
  { color: "#dcdfe6" },
  { color: "#409eff" },
  { color: "#f56c6c" },
  { color: "#e6a23c" },
  { color: "#5cb87a" },
];

// 当前图表的自定义配色方案
const currentCustomSchemes = computed(() => {
  return visualStore.chartConfig[visualStore.chartType].customColorSchemes || {};
});

// 当前是否可以编辑配色方案
const canEditCurrentScheme = computed(() => {
  return styleType.value !== "default";
});

// 当前是否可以删除配色方案
const canDeleteCurrentScheme = computed(() => {
  return styleType.value !== "default" && currentCustomSchemes.value[styleType.value];
});

// 响应式的样式颜色，根据当前选择的配色方案动态更新
const styleColor = ref([]);

// 添加主题对话框相关
const showAddSchemeDialog = ref(false);
const newSchemeForm = ref({
  name: '',
  copyFrom: 'default'
});

// 柱状图相关计算属性
const barWidth = computed({
  get: () => {
    if (visualStore.chartType === 'barConfig') {
      return visualStore.chartConfig.barConfig.series[0]?.barWidth || 24;
    }
    return 24;
  },
  set: (value) => {
    if (visualStore.chartType === 'barConfig') {
      visualStore.chartConfig.barConfig.series[0].barWidth = value;
    }
  }
});

const barBorderRadius = computed({
  get: () => {
    if (visualStore.chartType === 'barConfig') {
      const borderRadius = visualStore.chartConfig.barConfig.series[0]?.itemStyle?.borderRadius;
      return Array.isArray(borderRadius) ? borderRadius[0] : (borderRadius || 0);
    }
    return 0;
  },
  set: (value) => {
    if (visualStore.chartType === 'barConfig') {
      visualStore.chartConfig.barConfig.series[0].itemStyle.borderRadius = [value, value, 0, 0];
    }
  }
});

// 条形图相关计算属性
const barHorizontalWidth = computed({
  get: () => {
    if (visualStore.chartType === 'barHorizontalConfig') {
      return visualStore.chartConfig.barHorizontalConfig.series[0]?.barWidth || 24;
    }
    return 24;
  },
  set: (value) => {
    if (visualStore.chartType === 'barHorizontalConfig') {
      const series = visualStore.chartConfig.barHorizontalConfig.series[0];
      if (series) {
        series.barWidth = value;
      }
    }
  }
});

const barHorizontalBorderRadius = computed({
  get: () => {
    if (visualStore.chartType === 'barHorizontalConfig') {
      const borderRadius = visualStore.chartConfig.barHorizontalConfig.series[0]?.itemStyle?.borderRadius;
      return Array.isArray(borderRadius) ? borderRadius[1] : (borderRadius || 0);
    }
    return 0;
  },
  set: (value) => {
    if (visualStore.chartType === 'barHorizontalConfig') {
      visualStore.chartConfig.barHorizontalConfig.series[0].itemStyle.borderRadius = [0, value, value, 0];
    }
  }
});

// 折线图相关计算属性
const lineWidth = computed({
  get: () => {
    if (visualStore.chartType === 'lineConfig') {
      return visualStore.chartConfig.lineConfig.series[0]?.lineStyle?.width || 3;
    }
    return 3;
  },
  set: (value) => {
    if (visualStore.chartType === 'lineConfig') {
      visualStore.chartConfig.lineConfig.series[0].lineStyle.width = value;
    }
  }
});

const lineSmooth = computed({
  get: () => {
    if (visualStore.chartType === 'lineConfig') {
      return visualStore.chartConfig.lineConfig.series[0]?.smooth || false;
    }
    return false;
  },
  set: (value) => {
    if (visualStore.chartType === 'lineConfig') {
      visualStore.chartConfig.lineConfig.series[0].smooth = value;
    }
  }
});

// 饼状图相关计算属性
const pieInnerRadius = computed({
  get: () => {
    if (visualStore.chartType === 'pieConfig') {
      const radius = visualStore.chartConfig.pieConfig.series[0]?.radius;
      if (Array.isArray(radius)) {
        return parseInt(radius[0]) || 40;
      }
    }
    return 40;
  },
  set: (value) => {
    if (visualStore.chartType === 'pieConfig') {
      const series = visualStore.chartConfig.pieConfig.series[0];
      if (series && Array.isArray(series.radius)) {
        series.radius[0] = `${value}%`;
      }
    }
  }
});

const pieOuterRadius = computed({
  get: () => {
    if (visualStore.chartType === 'pieConfig') {
      const radius = visualStore.chartConfig.pieConfig.series[0]?.radius;
      if (Array.isArray(radius)) {
        return parseInt(radius[1]) || 70;
      }
    }
    return 70;
  },
  set: (value) => {
    if (visualStore.chartType === 'pieConfig') {
      const series = visualStore.chartConfig.pieConfig.series[0];
      if (series && Array.isArray(series.radius)) {
        series.radius[1] = `${value}%`;
      }
    }
  }
});

const pieShowLabel = computed(() => {
  if (visualStore.chartType === 'pieConfig') {
    const series = visualStore.chartConfig.pieConfig.series[0];
    return series?.label?.show !== false;
  }
  return true;
});

const pieShowLabelLine = computed(() => {
  if (visualStore.chartType === 'pieConfig') {
    const series = visualStore.chartConfig.pieConfig.series[0];
    return series?.labelLine?.show !== false;
  }
  return true;
});

// 漏斗图相关计算属性
const funnelSort = computed(() => {
  if (visualStore.chartType === 'funnelConfig') {
    return visualStore.chartConfig.funnelConfig.series[0]?.sort || 'descending';
  }
  return 'descending';
});

const funnelGap = computed({
  get: () => {
    if (visualStore.chartType === 'funnelConfig') {
      return visualStore.chartConfig.funnelConfig.series[0]?.gap || 2;
    }
    return 2;
  },
  set: (value) => {
    if (visualStore.chartType === 'funnelConfig') {
      const series = visualStore.chartConfig.funnelConfig.series[0];
      if (series) {
        series.gap = value;
      }
    }
  }
});

// 雷达图相关计算属性
const radarRadius = computed({
  get: () => {
    if (visualStore.chartType === 'radarConfig') {
      const radius = visualStore.chartConfig.radarConfig.radar?.radius;
      return parseInt(radius) || 50;
    }
    return 50;
  },
  set: (value) => {
    if (visualStore.chartType === 'radarConfig') {
      const radar = visualStore.chartConfig.radarConfig.radar;
      if (radar) {
        radar.radius = `${value}%`;
      }
    }
  }
});

const radarShowSplitArea = computed({
  get: () => {
    if (visualStore.chartType === 'radarConfig') {
      return visualStore.chartConfig.radarConfig.radar?.splitArea?.show || true;
    }
    return true;
  },
  set: (value) => {
    if (visualStore.chartType === 'radarConfig') {
      const radar = visualStore.chartConfig.radarConfig.radar;
      if (radar) {
        if (!radar.splitArea) {
          radar.splitArea = {};
        }
        radar.splitArea.show = value;
      }
    }
  }
});

// 仪表图相关计算属性
const gaugeRadius = computed({
  get: () => {
    if (visualStore.chartType === 'gaugeConfig') {
      const radius = visualStore.chartConfig.gaugeConfig.series[0]?.radius;
      return parseInt(radius) || 75;
    }
    return 75;
  },
  set: (value) => {
    if (visualStore.chartType === 'gaugeConfig') {
      const series = visualStore.chartConfig.gaugeConfig.series[0];
      if (series) {
        series.radius = `${value}%`;
      }
    }
  }
});

const gaugeMin = computed({
  get: () => {
    if (visualStore.chartType === 'gaugeConfig') {
      return visualStore.chartConfig.gaugeConfig.series[0]?.min || 0;
    }
    return 0;
  },
  set: (value) => {
    if (visualStore.chartType === 'gaugeConfig') {
      const series = visualStore.chartConfig.gaugeConfig.series[0];
      if (series) {
        series.min = value;
      }
    }
  }
});

const gaugeMax = computed({
  get: () => {
    if (visualStore.chartType === 'gaugeConfig') {
      return visualStore.chartConfig.gaugeConfig.series[0]?.max || 1000;
    }
    return 1000;
  },
  set: (value) => {
    if (visualStore.chartType === 'gaugeConfig') {
      const series = visualStore.chartConfig.gaugeConfig.series[0];
      if (series) {
        series.max = value;
      }
    }
  }
});
/**
 * 处理颜色变化 - 支持不同图表类型
 *
 * @param item 当前选中项
 * @param index 当前选中项的索引
 */
const handleColorChange = (item, index) => {
  // 如果是默认主题，不允许修改
  if (styleType.value === "default") {
    ElMessage.warning('舒适主题不可修改');
    return;
  }

  // 获取当前选择的配色方案
  const currentScheme = styleType.value;

  // 更新当前显示的 styleColor 数组
  styleColor.value[index].color = item.color;

  // 获取更新后的完整颜色数组
  const newColors = styleColor.value.map((colorItem) => colorItem.color);

  // 更新自定义配色方案数据
  if (currentCustomSchemes.value[currentScheme]) {
    visualStore.chartConfig[visualStore.chartType].customColorSchemes[currentScheme] = [...newColors];
  }

  // 更新当前图表类型的颜色配置
  visualStore.chartConfig[visualStore.chartType].color = newColors;

  // 针对不同图表类型进行特殊处理
  if (visualStore.chartType === 'lineConfig') {
    // 折线图需要同时更新 lineStyle 和 itemStyle 的颜色
    const series = visualStore.chartConfig.lineConfig.series[0];
    if (series) {
      series.lineStyle.color = newColors[0]; // 使用第一个颜色作为线条颜色
      series.itemStyle.color = newColors[0]; // 使用第一个颜色作为点的颜色

      // 如果有区域填充，也更新区域颜色
      if (series.areaStyle && series.areaStyle.color && series.areaStyle.color.colorStops) {
        series.areaStyle.color.colorStops[0].color = newColors[0];
        series.areaStyle.color.colorStops[1].color = newColors[0] + '1A'; // 添加透明度
      }
    }
  } else if (visualStore.chartType === 'pieConfig') {
    // 饼状图需要为每个数据项设置颜色
    const series = visualStore.chartConfig.pieConfig.series[0];
    if (series && series.data) {
      series.data.forEach((dataItem, index) => {
        if (newColors[index % newColors.length]) {
          dataItem.itemStyle = dataItem.itemStyle || {};
          dataItem.itemStyle.color = newColors[index % newColors.length];
        }
      });
    }
  } else if (visualStore.chartType === 'funnelConfig') {
    // 漏斗图需要为每个数据项设置颜色
    const series = visualStore.chartConfig.funnelConfig.series[0];
    if (series && series.data) {
      series.data.forEach((dataItem, index) => {
        if (newColors[index % newColors.length]) {
          dataItem.itemStyle = dataItem.itemStyle || {};
          dataItem.itemStyle.color = newColors[index % newColors.length];
        }
      });
    }
  } else if (visualStore.chartType === 'radarConfig') {
    // 雷达图需要为每个系列设置颜色
    const series = visualStore.chartConfig.radarConfig.series[0];
    if (series && series.data) {
      series.data.forEach((dataItem, index) => {
        if (newColors[index % newColors.length]) {
          dataItem.itemStyle = dataItem.itemStyle || {};
          dataItem.itemStyle.color = newColors[index % newColors.length];
          dataItem.areaStyle = dataItem.areaStyle || {};
          dataItem.areaStyle.color = newColors[index % newColors.length];
        }
      });
    }
  } else if (visualStore.chartType === 'gaugeConfig') {
    // 仪表图设置指针和详情颜色
    const series = visualStore.chartConfig.gaugeConfig.series[0];
    if (series) {
      series.pointer = series.pointer || {};
      series.pointer.itemStyle = series.pointer.itemStyle || {};
      series.pointer.itemStyle.color = newColors[0] || '#00d4ff';

      series.detail = series.detail || {};
      series.detail.color = newColors[0] || '#00d4ff';
    }
  }

  console.log(`颜色已更新 - 图表类型: ${visualStore.chartType}, 配色方案: ${currentScheme}, 索引: ${index}, 颜色: ${item.color}`);
};

// 获取指定配色方案的颜色数组
const getSchemeColors = (schemeName) => {
  if (schemeName === "default") {
    return [...defaultColorScheme];
  } else {
    const customScheme = currentCustomSchemes.value[schemeName];
    if (customScheme) {
      return customScheme.map(color => ({ color }));
    }
  }
  return [...defaultColorScheme];
};

// 初始化当前图表的颜色显示
const initCurrentChartColors = () => {
  const savedScheme = visualStore.chartConfig[visualStore.chartType].selectedColorScheme || "default";
  styleColor.value = getSchemeColors(savedScheme);
};

// 添加新的自定义主题
const addNewScheme = () => {
  if (!newSchemeForm.value.name.trim()) {
    ElMessage.warning('请输入主题名称');
    return;
  }

  if (newSchemeForm.value.name === 'default') {
    ElMessage.warning('主题名称不能为"default"');
    return;
  }

  if (currentCustomSchemes.value[newSchemeForm.value.name]) {
    ElMessage.warning('主题名称已存在');
    return;
  }

  // 获取要复制的配色方案
  const sourceColors = getSchemeColors(newSchemeForm.value.copyFrom);
  const newColors = sourceColors.map(item => item.color);

  // 添加到当前图表的自定义配色方案中
  if (!visualStore.chartConfig[visualStore.chartType].customColorSchemes) {
    visualStore.chartConfig[visualStore.chartType].customColorSchemes = {};
  }

  visualStore.chartConfig[visualStore.chartType].customColorSchemes[newSchemeForm.value.name] = newColors;

  // 切换到新创建的主题
  styleType.value = newSchemeForm.value.name;
  initCurrentChartColors();

  // 重置表单并关闭对话框
  newSchemeForm.value = { name: '', copyFrom: 'default' };
  showAddSchemeDialog.value = false;

  ElMessage.success('主题创建成功');
};

// 删除当前自定义主题
const deleteCurrentScheme = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除主题"${styleType.value}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 删除自定义主题
    delete visualStore.chartConfig[visualStore.chartType].customColorSchemes[styleType.value];

    // 切换到默认主题
    styleType.value = "default";
    initCurrentChartColors();

    ElMessage.success('主题删除成功');
  } catch {
    // 用户取消删除
  }
};

const changeStyle = (style) => {
  // 保存当前图表的配色方案选择
  styleType.value = style;

  // 获取选择的配色方案颜色
  styleColor.value = getSchemeColors(style);

  // 应用新的配色方案到图表
  const newColors = styleColor.value.map(item => item.color);
  visualStore.chartConfig[visualStore.chartType].color = newColors;

  // 针对不同图表类型进行特殊处理
  if (visualStore.chartType === 'lineConfig') {
    const series = visualStore.chartConfig.lineConfig.series[0];
    if (series) {
      series.lineStyle.color = newColors[0];
      series.itemStyle.color = newColors[0];
      if (series.areaStyle && series.areaStyle.color && series.areaStyle.color.colorStops) {
        series.areaStyle.color.colorStops[0].color = newColors[0];
        series.areaStyle.color.colorStops[1].color = newColors[0] + '1A';
      }
    }
  } else if (visualStore.chartType === 'pieConfig') {
    const series = visualStore.chartConfig.pieConfig.series[0];
    if (series && series.data) {
      series.data.forEach((dataItem, index) => {
        if (newColors[index % newColors.length]) {
          dataItem.itemStyle = dataItem.itemStyle || {};
          dataItem.itemStyle.color = newColors[index % newColors.length];
        }
      });
    }
  } else if (visualStore.chartType === 'funnelConfig') {
    const series = visualStore.chartConfig.funnelConfig.series[0];
    if (series && series.data) {
      series.data.forEach((dataItem, index) => {
        if (newColors[index % newColors.length]) {
          dataItem.itemStyle = dataItem.itemStyle || {};
          dataItem.itemStyle.color = newColors[index % newColors.length];
        }
      });
    }
  } else if (visualStore.chartType === 'radarConfig') {
    const series = visualStore.chartConfig.radarConfig.series[0];
    if (series && series.data) {
      series.data.forEach((dataItem, index) => {
        if (newColors[index % newColors.length]) {
          dataItem.itemStyle = dataItem.itemStyle || {};
          dataItem.itemStyle.color = newColors[index % newColors.length];
          dataItem.areaStyle = dataItem.areaStyle || {};
          dataItem.areaStyle.color = newColors[index % newColors.length];
        }
      });
    }
  } else if (visualStore.chartType === 'gaugeConfig') {
    const series = visualStore.chartConfig.gaugeConfig.series[0];
    if (series) {
      series.pointer = series.pointer || {};
      series.pointer.itemStyle = series.pointer.itemStyle || {};
      series.pointer.itemStyle.color = newColors[0] || '#00d4ff';

      series.detail = series.detail || {};
      series.detail.color = newColors[0] || '#00d4ff';
    }
  }
};

// 柱状图相关处理函数
const handleBarWidthChange = (value) => {
  barWidth.value = value;
};

const handleBarBorderRadiusChange = (value) => {
  barBorderRadius.value = value;
};

// 条形图相关处理函数
const handleBarHorizontalWidthChange = (value) => {
  barHorizontalWidth.value = value;
};

const handleBarHorizontalBorderRadiusChange = (value) => {
  barHorizontalBorderRadius.value = value;
};

// 折线图相关处理函数
const handleLineWidthChange = (value) => {
  lineWidth.value = value;
};

const handleLineSmoothChange = (value) => {
  lineSmooth.value = value;
};

// 饼状图相关处理函数
const handlePieInnerRadiusChange = (value) => {
  pieInnerRadius.value = value;
};

const handlePieOuterRadiusChange = (value) => {
  pieOuterRadius.value = value;
};

const handlePieShowLabelChange = (value) => {
  console.log('handlePieShowLabelChange called with value:', value);
  if (visualStore.chartType === 'pieConfig') {
    const series = visualStore.chartConfig.pieConfig.series[0];
    if (series) {
      // 确保 label 对象存在
      if (!series.label) {
        series.label = {
          show: true,
          formatter: "{b}: {d}%"
        };
      }
      series.label.show = value;
      console.log('Label show set to:', value, 'Current series.label:', series.label);
    }
  }
};

const handlePieShowLabelLineChange = (value) => {
  console.log('handlePieShowLabelLineChange called with value:', value);
  if (visualStore.chartType === 'pieConfig') {
    const series = visualStore.chartConfig.pieConfig.series[0];
    if (series) {
      // 确保 labelLine 对象存在
      if (!series.labelLine) {
        series.labelLine = {
          show: true
        };
      }
      series.labelLine.show = value;
      console.log('LabelLine show set to:', value, 'Current series.labelLine:', series.labelLine);
    }
  }
};

// 漏斗图相关处理函数
const handleFunnelSortChange = (value) => {
  if (visualStore.chartType === 'funnelConfig') {
    const series = visualStore.chartConfig.funnelConfig.series[0];
    if (series) {
      series.sort = value;
    }
  }
};

const handleFunnelGapChange = (value) => {
  if (visualStore.chartType === 'funnelConfig') {
    const series = visualStore.chartConfig.funnelConfig.series[0];
    if (series) {
      series.gap = value;
      console.log('Funnel gap changed to:', value);
    }
  }
};

// 雷达图相关处理函数
const handleRadarRadiusChange = (value) => {
  if (visualStore.chartType === 'radarConfig') {
    const radar = visualStore.chartConfig.radarConfig.radar;
    if (radar) {
      radar.radius = `${value}%`;
      console.log('Radar radius changed to:', `${value}%`);
    }
  }
};

const handleRadarShowSplitAreaChange = (value) => {
  if (visualStore.chartType === 'radarConfig') {
    const radar = visualStore.chartConfig.radarConfig.radar;
    if (radar) {
      if (!radar.splitArea) {
        radar.splitArea = {};
      }
      radar.splitArea.show = value;
    }
  }
};

// 仪表图相关处理函数
const handleGaugeRadiusChange = (value) => {
  if (visualStore.chartType === 'gaugeConfig') {
    const series = visualStore.chartConfig.gaugeConfig.series[0];
    if (series) {
      series.radius = `${value}%`;
    }
  }
};

const handleGaugeMinChange = (value) => {
  if (visualStore.chartType === 'gaugeConfig') {
    const series = visualStore.chartConfig.gaugeConfig.series[0];
    if (series) {
      series.min = value;
    }
  }
};

const handleGaugeMaxChange = (value) => {
  if (visualStore.chartType === 'gaugeConfig') {
    const series = visualStore.chartConfig.gaugeConfig.series[0];
    if (series) {
      series.max = value;
    }
  }
};

// 监听图表类型变化，恢复该图表上次选择的配色方案
watch(() => visualStore.chartType, () => {
  initCurrentChartColors();
}, { immediate: false });

onMounted(() => {
  // 初始化时恢复当前图表上次选择的配色方案
  initCurrentChartColors();
});
</script>

<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18px;

  .form-label {
    width: 80px;
    color: #222;
    font-size: 14px;
    flex-shrink: 0;
    line-height: 32px;
  }

  .form-input {
    flex: 1;
    min-width: 0;
  }
}

.color-scheme-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;

  .scheme-select {
    flex: 1;
    min-width: 120px;
  }
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;

  .color-picker-item {
    margin-right: 4px;
    margin-bottom: 4px;
  }

  .readonly-tip {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
  }
}

.unit {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}

// 响应式布局
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .form-label {
      width: auto;
      line-height: 1.5;
    }
  }

  .color-scheme-controls {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;

    .scheme-select {
      width: 100%;
    }
  }

  .color-picker-container {
    width: 100%;
  }
}
</style>