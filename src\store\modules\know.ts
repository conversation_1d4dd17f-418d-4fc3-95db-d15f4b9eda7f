import {defineStore} from 'pinia';
import {store} from '@/store';

export const useKnowStore = defineStore('knowStore', {
  state: () => ({
    nounParamsForm: {} as any,
    knowParamsForm: {} as any,
    // 文档id
    knowDocumentId: '',
  }),
  actions: {
    setNoumParamsForm(params: any) {
      this.nounParamsForm = params;
    },
    setKnowParamsForm(params: any) {
      this.knowParamsForm = params;
    },
    setDocumentId(params: any) {
      this.knowDocumentId = params;
    },
    clearKnowParamsForm() {
      this.knowParamsForm = {};
    },
    clearNounParamsForm() {
      this.nounParamsForm = {};
    },
    clearDocumentId() {
      this.knowDocumentId = '';
    },
  },
  persist: [
    {
      key: 'KNOW_STORE',
      storage: localStorage,
      paths: ['nounParamsForm', 'knowParamsForm', 'knowDocumentId'], // 新增knowParamsForm字段
    },
  ],
});

export const useKnowStoreWithOut = () => {
  return useKnowStore(store);
};
