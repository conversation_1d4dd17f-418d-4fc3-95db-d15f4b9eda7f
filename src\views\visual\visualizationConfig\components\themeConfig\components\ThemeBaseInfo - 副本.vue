<template>
  <div>
    <div class="form-row">
      <label class="form-label">主题名称</label>
      <el-input class="form-input" placeholder="请输入主题名称" v-model="visualStore.pageConfig.basicConfig.themeTitle" />
    </div>
    <div class="form-row">
      <label class="form-label">主题封面</label>
      <el-popover placement="top" :width="365" trigger="click">
        <template #default>
          <div class="upload-container">
            <el-upload
              class="upload-demo"
              drag
              action="#"
              :show-file-list="false"
              :limit="1"
              :auto-upload="false"
              :file-list="fileList"
              @change="handleFileChange"
              accept=".jpg,.jpeg,.png,.gif,.svg"
            >
              <el-button type="primary" plain :icon="Upload">上传本地图片</el-button>
              <div class="upload-tip">只支持 jpg, jpeg, png, gif, svg 格式，最大 1M</div>
            </el-upload>

            <div class="upload-link-row">
              <el-input v-model="customImgUrl" placeholder="请输入图片网址" class="upload-link-input" />
              <el-button type="primary" @click="useCustomImg" :disabled="!customImgUrl">使用</el-button>
            </div>

            <div class="image-popover-footer">
              <el-button type="primary" link @click="visualStore.pageConfig.basicConfig.themeCover = ''">清空图片</el-button>
            </div>
          </div>
        </template>

        <template #reference>
          <div class="image-upload-placeholder" v-if="visualStore.pageConfig.basicConfig.themeCover === ''">
            <span class="plus">+</span>
          </div>
          <img v-else class="image-upload-placeholder-img" :src="visualStore.pageConfig.basicConfig.themeCover" alt="" />
        </template>
      </el-popover>
    </div>
  </div>
</template>

<script setup>
import {ref} from 'vue';
import {useVisualStore} from '@/store/modules/visual';
import {Upload} from '@element-plus/icons-vue';
import {ElMessage} from 'element-plus';

const visualStore = useVisualStore();

const customImgUrl = ref('');
const fileList = ref([]);

/**
 * 处理本地图片上传
 * @param {Object} file - 上传的文件对象
 */
function handleFileChange(file) {
  const raw = file.raw || file;

  // 文件类型和大小判断
  const isImage = /\.(jpg|jpeg|png|gif|svg)$/i.test(raw.name);
  const isLt1M = raw.size / 1024 / 1024 < 1;

  if (!isImage) {
    ElMessage.error('只支持 jpg、jpeg、png、gif、svg 格式');
    return;
  }
  if (!isLt1M) {
    ElMessage.error('图片大小不能超过 1M');
    return;
  }

  // 读取为 base64 预览
  const reader = new FileReader();
  reader.onload = e => {
    visualStore.pageConfig.basicConfig.themeCover = e.target.result;
    ElMessage.success('本地预览已更新');
  };
  reader.readAsDataURL(raw);

  // 清空 fileList 防止重复不触发
  fileList.value = [];
}

/**
 * 使用外链图片
 */
function useCustomImg() {
  visualStore.pageConfig.basicConfig.themeCover = customImgUrl.value;
  ElMessage.success('已使用图片链接');
}
</script>

<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;

  .form-label {
    width: 80px;
    color: #222;
    font-size: 14px;
    flex-shrink: 0;
  }

  .form-input {
    flex: 1;
    min-width: 0;
  }

  .image-upload-placeholder,
  .image-upload-placeholder-img {
    width: 100px;
    height: 70px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ddd;
    margin-left: 8px;

    .plus {
      font-size: 32px;
      color: #666;
      font-weight: bold;
    }
  }

  .image-upload-placeholder-img {
    object-fit: cover;
    background: none;
  }
}

// 上传容器
.upload-container {
  padding: 16px;
}

// 上传模块
.upload-demo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  ::v-deep(.el-upload-dragger) {
    border: none;
    padding: 0;
    text-align: left;
  }
}

.upload-tip {
  text-align: left;
  color: #888;
  font-size: 13px;
  margin-top: 15px;
}

.upload-link-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;

  .upload-link-input {
    flex: 1;
  }
}

// Popover 底部清空按钮
.image-popover-footer {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ccc;
  text-align: right;
}
</style>
