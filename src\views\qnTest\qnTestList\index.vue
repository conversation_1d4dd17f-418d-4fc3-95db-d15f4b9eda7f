<template>
  <PageHeader title="问数测试">
    <template #default>
      <div class="content-header">
        <!-- 搜索长度和类型 -->
        <el-input v-model="searchText" placeholder="输入关键字搜索测试问题" class="search-input" :prefix-icon="Search" clearable @keyup.enter="searchAndReset" />
        <div class="header-buttons">
          <!-- 下拉菜单 -->
          <el-dropdown style="margin-right: 15px">
            <el-button type="primary">
              批量操作
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="item in dropdownList" :key="item.type" @click="bulkOperations(item.type)">
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <!-- 添加按钮 -->
          <el-button type="primary" class="create-btn" @click="addQuestTest">添加测试问题</el-button>
        </div>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <!-- 表格 -->
      <el-table :data="tableData" style="width: 100%" max-height="630px" @selection-change="handleSelectionChange">
        <!-- 表格选择框 -->
        <el-table-column type="selection" width="55" />
        <!-- 测试问题列 -->
        <el-table-column prop="testQuestions" label="测试问题" />
        <!-- 正确答案列 -->
        <el-table-column prop="correctAnswer" label="正确答案" />
        <!-- 是否正确列 -->
        <el-table-column prop="istrue" label="是否正确">
          <template #default="scope">
            <span v-if="scope.row.istrue">✓</span>
            <span v-else>✕</span>
          </template>
        </el-table-column>
        <!-- 错误原因列 -->
        <el-table-column prop="errorReason" label="错误原因" />
        <!-- 操作列 -->
        <el-table-column label="操作" width="360" align="center">
          <template #default="scope">
            <!-- 添加至缓存按钮 -->
            <el-button type="primary" link @click="addToCache(scope.row)" v-show="scope.row.istrue">添加至缓存</el-button>
            <!-- 添加至训练集按钮 -->
            <el-button type="primary" link @click="addToTrainingSet(scope.row)">添加至训练集</el-button>
            <!-- 删除按钮 -->
            <el-button type="danger" link @click="deleteRow(scope.row.id)">删除</el-button>
            <!-- 查看日志按钮 -->
            <el-button type="primary" link @click="viewLog(scope.row.id)">查看日志</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="content-footer">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        ></el-pagination>
      </div>
    </div>
  </el-card>
  <!-- 批量添加对话框 -->
  <addInBulkDialog ref="addInBulkDialogRef" @getTableData="getTableData" @resetPagination="resetPagination" />
</template>

<script lang="ts" setup>
import {ref, computed, onMounted} from 'vue';
import {ArrowDown, Search} from '@element-plus/icons-vue';
import {useRouter} from 'vue-router';
import {ElMessage, ElMessageBox} from 'element-plus';
import {useQnTestStore} from '@/store/modules/qnTest';
import * as api from '@/api/qnTest/qnTest/index';
import addInBulkDialog from './components/addInBulkDialog.vue';
import PageHeader from '@/components/PageHeader.vue';

const router = useRouter();
const tableData = ref<any[]>([]);
const qnTestStore = useQnTestStore();

const addInBulkDialogRef = ref<any | null>(null);
const dropdownList = ref([
  {label: '批量添加', type: 'add'},
  {label: '批量删除', type: 'delete'},
  {label: '批量添加至训练集', type: 'addTraining'},
  {label: '批量添加缓存', type: 'addCache'},
  {label: '批量下载日志', type: 'downloadLog'},
  {label: '批量验证', type: 'verify'},
]);

const searchText = ref('');
let selectionList = ref<any[]>([]);

// 生命周期钩子：组件挂载后初始化数据
onMounted(() => {
  getTableData();
});

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: computed(() => tableData.value.length),
});

// 重置分页
const resetPagination = () => {
  pagination.value.currentPage = 1;
  pagination.value.pageSize = 10;
};

// 搜索框回车事件
const searchAndReset = () => {
  resetPagination();
  getTableData();
};

// 获取表格数据
const getTableData = async () => {
  try {
    const res: any = await api.askNumberPage({
      pageNo: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
      testQuestions: searchText.value,
    });
    tableData.value = res?.list || [];
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// 分页事件
const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val;
  getTableData();
};
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val;
  getTableData();
};

// 批量操作
const bulkOperations = (type: string) => {
  switch (type) {
    case 'add':
      addInBulkDialogRef.value?.openDialog?.();
      break;
    case 'delete':
      deleteInBulk();
      break;
    case 'addTraining':
      console.log('执行批量添加至训练集');
      break;
    case 'addCache':
      console.log('执行批量添加缓存');
      break;
    case 'downloadLog':
      console.log('执行批量下载日志');
      break;
    case 'verify':
      if (selectionList.value.length === 0) {
        ElMessage.warning('请先选择要验证的项');
        return;
      }
      batchVerification();
      break;
    default:
      console.warn('未知操作类型:', type);
  }
};

// 批量验证
const batchVerification = () => {
  router.push({
    name: 'AddQnTest',
  });
  qnTestStore.setAddQnTest({
    type: 'verify',
    selectList: selectionList.value,
  });
};

// 表格选择事件
const handleSelectionChange = (selection: any[]) => {
  selectionList.value = selection;
};

// 添加测试问题
const addQuestTest = () => {
  router.push({
    name: 'AddQnTest',
  });
  qnTestStore.setAddQnTest({
    type: 'add',
  });
};

// 添加至缓存
const addToCache = (row: any) => {
  console.log('添加至缓存:', row);
};

// 添加至训练集
const addToTrainingSet = (row: any) => {
  console.log('添加至训练集:', row);
};

// 单行删除
const deleteRow = (id: any) => {
  ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const formData = new URLSearchParams();
        formData.append('ids', id.toString());
        await api.askNumberDelete(formData);
        resetPagination();
        getTableData();
        ElMessage.success('删除成功');
      } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      /* 弹框取消处理 */
    });
};

// 批量删除
const deleteInBulk = () => {
  if (selectionList.value.length === 0) {
    ElMessage.warning('请先选择要删除的项');
    return;
  }
  ElMessageBox.confirm(`确定要删除选中的 ${selectionList.value.length} 个项吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const idsParam = selectionList.value.map((item: any) => item.id).join(',');
        const formData = new URLSearchParams();
        formData.append('ids', idsParam);
        await api.askNumberDelete(formData);
        resetPagination();
        getTableData();
        ElMessage.success('批量删除成功');
      } catch (error) {
        console.error('批量删除失败:', error);
        ElMessage.error('批量删除失败');
      }
    })
    .catch(() => {
      /* 弹框取消处理 */
    });
};

// 查看日志
const viewLog = (id: any) => {
  router.push({
    name: 'AddQnTest',
  });
  qnTestStore.setAddQnTest({
    id: id,
    type: 'view',
  });
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  height: 100%;

  .content-footer {
    margin-top: 20px;
  }
}
</style>
