import request from '@/config/axios';

// 创建知识管理-名词分组
export const groupCreate = async (data: any) => {
  return await request.post({url: '/knowledge/group/create', data});
};

// 知识管理获取名词分组
export const groupPage = async (params: any) => {
  return await request.get({url: `/knowledge/group/page`, params});
};
// 更新名词分组（用于改变权限）
export const groupUpdate = async (data: any) => {
  return await request.put({url: `/knowledge/group/update`, data});
};
// 知识管理获取名词分组
export const definitionsPage = async (params: any) => {
  return await request.get({url: `/knowledge/definitions/page`, params});
};
// 创建名词定义
export const definitionsCreate = async (data: any) => {
  return await request.post({url: `/knowledge/definitions/create`, data});
};
// 更新名词定义
export const definitionsUpdate = async (id: any, data: any) => {
  return await request.put({url: `/knowledge/definitions/${id}`, data});
};
// 删除名词定义
export const definitionsDelete = async (data: any) => {
  return await request.delete({
    url: `/knowledge/definitions/${data.id}`,
  });
};

// 名词类型列表
export const definitionsTypeList = async () => {
  return await request.get({url: `/knowledge/definitions/typeList`});
};
// 获取命中切片列表
export const definitionsHitCutList = async (params: any) => {
  return await request.get({url: `/knowledge/definitions/hitCutList`, params});
};
