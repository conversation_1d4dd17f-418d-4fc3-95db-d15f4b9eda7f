import type {RouteRecordRaw} from 'vue-router';

export const toolRoutes: RouteRecordRaw[] = [
  {
    path: '/tool',
    meta: {
      title: '工具管理',
      grouping: true,
      menu: true,
      icon: 'menu/toolManage',
    },
    children: [
      {
        path: 'toolconfig',
        name: 'ToolConfig',
        component: () => import('@/views/tool/toolConfig/index.vue'),
        meta: {
          title: '工具配置',
          menu: true,
          icon: 'menu/toolConfig',
        },
      },
    ],
  },
];
