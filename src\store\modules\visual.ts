import { defineStore } from "pinia";
import { store } from "@/store";

export const useVisualStore = defineStore("visual", {
  // 正确导出方式
  state: () => ({
    pageConfig: {
      basicConfig: {
        themeTitle: "可视化",
        themeCover: "",
        titleFont: {
          fontFamily: "Arial",
          fontSize: 16,
          fontWeight: "normal",
          fontStyle: "normal",
          textAlign: "center"
        },
      },
      globalStyle: {
        pageFonts: "",
        componentBorderRadius: "0px",
        componentSpacing: "5px",
      },
      pageLayout: {
        pageTitle: "show",
        pageBackgroundColor: "#edf2fa", // 页面背景颜色值（纯色）
        gradientColor:
          "linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 100%)", // 渐变色值
        colorMode: "solid", // 颜色模式：'solid'(纯色) 或 'gradient'(渐变)
        pageTopImg: "",
        pageBottomImg: "",
        pagePadding: "5px",
      },
    },
    chartType: "barConfig",
    chartConfig: {
      barConfig: {
        color: [
          "#409eff",
          "#f56c6c",
          "#e6a23c",
          "#5cb87a",
          "#909399",
          "#dcdfe6",
          "#409eff",
          "#f56c6c",
          "#e6a23c",
          "#5cb87a",
        ],
        backgroundColor: "#ccc",
        colorMode: "solid", // 颜色模式：'solid'(纯色) 或 'gradient'(渐变)
        selectedColorScheme: "default", // 记录当前选择的配色方案
        customColorSchemes: {
          // 自定义配色方案，每个图表独立
          "自定义1": [
            "#4eff00",
            "#f89063",
            "#e6a23e",
            "#5cb87a",
            "#909399",
            "#dcdfe6",
            "#409eff",
            "#f56c6c"
          ]
        },
        graphic: {
          type: "image",
          id: "background",
          style: {
            image: "", // 图片URL
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
          },
          z: -1, // 设置z值确保图片在图表内容之下
        },
        legend: {
          x: "left",
          y: "top",
          top: "2%", // 图例垂直位置
          orient: "horizontal", // 柱状图默认水平排列
          textStyle: {
            fontFamily: "Arial", // 字体系列
            fontStyle: "normal", // 字体样式 (normal/italic)
            color: "#000000", // 文本颜色
            fontSize: 14, // 字体大小
            fontWeight: "normal", // 字体粗细 (normal/bold)
          },
        },
        title: {
          text: "数据统计",
          left: "center",
          top: "1%", // 标题往上移
          textStyle: {
            fontSize: 16,
            fontWeight: "normal",
            fontStyle: "normal",
            fontFamily: "Arial",
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "16%", // 减少顶部间距，让图表内容往上移
          containLabel: true,
          show: false, // 不显示 ECharts 内置边框
          backgroundColor: "transparent",
        },
        cardStyle: {
          borderColor: "#000000",
          borderWidth: 1,
          borderStyle: "solid",
          borderRadius: 0,
        },
        xAxis: {
          type: "category",
          data: ["一月", "二月", "三月", "四月", "五月", "六月"],
          axisTick: {
            alignWithLabel: true,
          },
          splitLine: {
            show: true,
            interval: "auto",
            lineStyle: {
              color: "#000",
              width: 1,
              type: "solid",
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#000",
              width: 2,
              type: "solid",
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            interval: "auto",
            lineStyle: {
              color: "#000",
              width: 1,
              type: "solid",
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#000",
              width: 2,
              type: "solid",
            },
          },
        },
        series: [
          {
            name: "销售额",
            type: "bar",
            barWidth: 24,
            data: [120, 200, 150, 80, 70, 110],
            itemStyle: {
              borderRadius: [5, 5, 0, 0],
              //   color:
              //     "LinearGradient(0, 0, 0, 1, [{offset:0,color:'#83bff6'},{offset:0.5,color:'#188df0'},{offset:1,color:'#188df0'}])",
            },
          },
        ],
      },
      pieConfig: {
        color: [
          "#409eff",
          "#f56c6c",
          "#e6a23c",
          "#5cb87a",
          "#909399",
          "#dcdfe6",
          "#409eff",
          "#f56c6c",
          "#e6a23c",
          "#5cb87a",
        ],
        backgroundColor: "#ccc",
        colorMode: "solid", // 颜色模式：'solid'(纯色) 或 'gradient'(渐变)
        selectedColorScheme: "default", // 记录当前选择的配色方案
        customColorSchemes: {
          // 自定义配色方案，每个图表独立
          "自定义1": [
            "#4eff00",
            "#f89063",
            "#e6a23e",
            "#5cb87a",
            "#909399",
            "#dcdfe6",
            "#409eff",
            "#f56c6c"
          ]
        },
        graphic: {
          type: "image",
          id: "background",
          style: {
            image: "", // 图片URL
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
          },
          z: -1, // 设置z值确保图片在图表内容之下
        },
        legend: {
          x: "right",
          y: "center",
          top: "middle", // 饼图图例垂直居中
          orient: "vertical", // 饼图默认垂直排列
          textStyle: {
            fontFamily: "Arial", // 字体系列
            fontStyle: "normal", // 字体样式 (normal/italic)
            color: "#000000", // 文本颜色
            fontSize: 14, // 字体大小
            fontWeight: "normal", // 字体粗细 (normal/bold)
          },
        },
        title: {
          text: "数据统计",
          left: "center",
          top: "1%", // 标题往上移
          textStyle: {
            fontSize: 16,
            fontWeight: "normal",
            fontStyle: "normal",
            fontFamily: "Arial",
          },
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },
        cardStyle: {
          borderColor: "#000000",
          borderWidth: 1,
          borderStyle: "solid",
          borderRadius: 0,
        },
        series: [
          {
            name: "数据分布",
            type: "pie",
            radius: ["30%", "50%"], // 环形饼图
            center: ["30%", "50%"], // 饼图位置，为图例留出空间
            data: [
              { value: 335, name: "直接访问" },
              { value: 310, name: "邮件营销" },
              { value: 234, name: "联盟广告" },
              { value: 135, name: "视频广告" },
              { value: 1548, name: "搜索引擎" }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)"
              }
            },
            label: {
              show: true,
              formatter: "{b}: {d}%"
            },
            labelLine: {
              show: true
            }
          },
        ],
      },
      // 条形图配置（横向柱状图）
      barHorizontalConfig: {
        color: [
          "#409eff",
          "#f56c6c",
          "#e6a23c",
          "#5cb87a",
          "#909399",
          "#dcdfe6",
          "#409eff",
          "#f56c6c",
          "#e6a23c",
          "#5cb87a",
        ],
        backgroundColor: "#ccc",
        colorMode: "solid",
        selectedColorScheme: "default",
        customColorSchemes: {
          "自定义1": [
            "#4eff00",
            "#f89063",
            "#e6a23e",
            "#5cb87a",
            "#909399",
            "#dcdfe6",
            "#409eff",
            "#f56c6c"
          ]
        },
        graphic: {
          type: "image",
          id: "background",
          style: {
            image: "",
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
          },
          z: -1,
        },
        legend: {
          x: "left",
          y: "top",
          top: "2%",
          orient: "horizontal", // 条形图默认水平排列
          textStyle: {
            fontFamily: "Arial",
            fontStyle: "normal",
            color: "#000000",
            fontSize: 14,
            fontWeight: "normal",
          },
        },
        title: {
          text: "数据统计",
          left: "center",
          top: "1%",
          textStyle: {
            fontSize: 16,
            fontWeight: "normal",
            fontStyle: "normal",
            fontFamily: "Arial",
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "15%", // 条形图需要更多左侧空间显示Y轴标签
          right: "4%",
          bottom: "3%",
          top: "16%",
          containLabel: true,
          show: false,
          backgroundColor: "transparent",
        },
        cardStyle: {
          borderColor: "#000000",
          borderWidth: 1,
          borderStyle: "solid",
          borderRadius: 0,
        },
        xAxis: {
          type: "value", // 条形图X轴为数值轴
          splitLine: {
            show: true,
            interval: "auto",
            lineStyle: {
              color: "#000",
              width: 1,
              type: "solid",
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#000",
              width: 1,
              type: "solid",
            },
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: "#000",
              width: 1,
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#000",
              fontSize: 12,
            },
          },
        },
        yAxis: {
          type: "category", // 条形图Y轴为类目轴
          data: ["一月", "二月", "三月", "四月", "五月", "六月"],
          splitLine: {
            show: false,
            lineStyle: {
              color: "#000",
              width: 1,
              type: "solid",
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#000",
              width: 1,
              type: "solid",
            },
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: "#000",
              width: 1,
            },
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#000",
              fontSize: 12,
            },
          },
        },
        series: [
          {
            name: "销量",
            type: "bar",
            data: [120, 200, 150, 80, 70, 110],
            barWidth: 24,
            itemStyle: {
              borderRadius: [0, 4, 4, 0], // 条形图右侧圆角
            },
          },
        ],
      },
      lineConfig: {
        color: [
          "#409eff",
          "#f56c6c",
          "#e6a23c",
          "#5cb87a",
          "#909399",
          "#dcdfe6",
          "#409eff",
          "#f56c6c",
          "#e6a23c",
          "#5cb87a",
        ],
        backgroundColor: "#ccc",
        colorMode: "solid", // 颜色模式：'solid'(纯色) 或 'gradient'(渐变)
        selectedColorScheme: "default", // 记录当前选择的配色方案
        customColorSchemes: {
          // 自定义配色方案，每个图表独立
          "自定义1": [
            "#4eff00",
            "#f89063",
            "#e6a23e",
            "#5cb87a",
            "#909399",
            "#dcdfe6",
            "#409eff",
            "#f56c6c"
          ]
        },
        graphic: {
          type: "image",
          id: "background",
          style: {
            image: "", // 图片URL
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
          },
          z: -1, // 设置z值确保图片在图表内容之下
        },
        legend: {
          x: "left",
          y: "top",
          top: "2%", // 图例垂直位置
          orient: "horizontal", // 折线图默认水平排列
          textStyle: {
            fontFamily: "Arial", // 字体系列
            fontStyle: "normal", // 字体样式 (normal/italic)
            color: "#000000", // 文本颜色
            fontSize: 14, // 字体大小
            fontWeight: "normal", // 字体粗细 (normal/bold)
          },
        },
        title: {
          text: "数据统计",
          left: "center",
          top: "1%", // 标题往上移
          textStyle: {
            fontSize: 16,
            fontWeight: "normal",
            fontStyle: "normal",
            fontFamily: "Arial",
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "16%", // 减少顶部间距，让图表内容往上移
          containLabel: true,
          show: false, // 不显示 ECharts 内置边框
          backgroundColor: "transparent",
        },
        cardStyle: {
          borderColor: "#000000",
          borderWidth: 1,
          borderStyle: "solid",
          borderRadius: 0,
        },
        xAxis: {
          type: "category",
          data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
          axisTick: {
            alignWithLabel: true,
          },
          splitLine: {
            show: true,
            interval: "auto",
            lineStyle: {
              color: "#000",
              width: 1,
              type: "solid",
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#000",
              width: 2,
              type: "solid",
            },
          },
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            interval: "auto",
            lineStyle: {
              color: "#000",
              width: 1,
              type: "solid",
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#000",
              width: 2,
              type: "solid",
            },
          },
        },
        series: [
          {
            name: "数据趋势",
            type: "line",
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            smooth: true, // 平滑曲线
            lineStyle: {
              width: 3,
              color: "#409eff",
            },
            itemStyle: {
              color: "#409eff",
              borderWidth: 2,
              borderColor: "#fff",
            },
            areaStyle: {
              opacity: 0.3,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#409eff'
                }, {
                  offset: 1, color: 'rgba(64, 158, 255, 0.1)'
                }]
              }
            },
          },
        ],
      },
      // 漏斗图配置
      funnelConfig: {
        color: [
          "#5470c6",
          "#91cc75",
          "#fac858",
          "#ee6666",
          "#73c0de",
          "#3ba272",
          "#fc8452",
          "#9a60b4",
          "#ea7ccc"
        ],
        backgroundColor: "#ccc",
        colorMode: "solid",
        selectedColorScheme: "default",
        customColorSchemes: {
          "自定义1": [
            "#4eff00",
            "#f89063",
            "#e6a23e",
            "#5cb87a",
            "#909399"
          ]
        },
        graphic: {
          type: "image",
          id: "background",
          style: {
            image: "",
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
          },
          z: -1,
        },
        legend: {
          x: "right",
          y: "center",
          top: "middle",
          orient: "vertical", // 漏斗图默认垂直排列
          textStyle: {
            fontFamily: "Arial",
            fontStyle: "normal",
            color: "#000000",
            fontSize: 14,
            fontWeight: "normal",
          },
        },
        title: {
          text: "数据图表",
          left: "center",
          top: "1%",
          textStyle: {
            fontSize: 16,
            fontWeight: "normal",
            fontStyle: "normal",
            fontFamily: "Arial",
          },
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },
        cardStyle: {
          borderColor: "#000000",
          borderWidth: 1,
          borderStyle: "solid",
          borderRadius: 0,
        },
        series: [
          {
            name: "漏斗图",
            type: "funnel",
            left: "10%",
            top: "10%",
            width: "60%",
            height: "80%",
            min: 0,
            max: 100,
            minSize: "0%",
            maxSize: "100%",
            sort: "descending",
            gap: 2,
            label: {
              show: true,
              position: "inside",
              formatter: "{b}: {c}%"
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: "solid"
              }
            },
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1
            },
            emphasis: {
              label: {
                fontSize: 20
              }
            },
            data: [
              { value: 100, name: "产品浏览" },
              { value: 80, name: "点击咨询" },
              { value: 60, name: "访客咨询" },
              { value: 40, name: "电话咨询" },
              { value: 20, name: "完成交易" }
            ]
          }
        ],
      },
      // 雷达图配置
      radarConfig: {
        color: [
          "#ff6b6b",
          "#4ecdc4",
          "#45b7d1",
          "#96ceb4",
          "#feca57",
          "#ff9ff3",
          "#54a0ff"
        ],
        backgroundColor: "#ccc",
        colorMode: "solid",
        selectedColorScheme: "default",
        customColorSchemes: {
          "自定义1": [
            "#4eff00",
            "#f89063",
            "#e6a23e",
            "#5cb87a"
          ]
        },
        graphic: {
          type: "image",
          id: "background",
          style: {
            image: "",
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
          },
          z: -1,
        },
        legend: {
          x: "center",
          y: "bottom",
          top: "90%",
          orient: "vertical", // 雷达图默认垂直排列
          textStyle: {
            fontFamily: "Arial",
            fontStyle: "normal",
            color: "#000000",
            fontSize: 14,
            fontWeight: "normal",
          },
        },
        title: {
          text: "雷达图",
          left: "center",
          top: "1%",
          textStyle: {
            fontSize: 16,
            fontWeight: "normal",
            fontStyle: "normal",
            fontFamily: "Arial",
          },
        },
        tooltip: {
          trigger: "item",
        },
        cardStyle: {
          borderColor: "#000000",
          borderWidth: 1,
          borderStyle: "solid",
          borderRadius: 0,
        },
        radar: {
          indicator: [
            { name: "销售一", max: 10000 },
            { name: "销售二", max: 10000 },
            { name: "销售三", max: 10000 },
            { name: "销售四", max: 10000 },
            { name: "销售五", max: 10000 },
            { name: "销售六", max: 10000 }
          ],
          center: ["50%", "50%"],
          radius: "50%",
          axisName: {
            color: "#000",
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              color: "#ccc"
            }
          },
          splitArea: {
            show: true
          },
          axisLine: {
            lineStyle: {
              color: "#ccc"
            }
          }
        },
        series: [
          {
            name: "数据一",
            type: "radar",
            data: [
              {
                value: [8000, 7000, 6000, 5000, 4000, 3000],
                name: "数据一",
                areaStyle: {
                  opacity: 0.3
                }
              },
              {
                value: [6000, 8000, 7000, 6000, 5000, 4000],
                name: "数据二",
                areaStyle: {
                  opacity: 0.3
                }
              }
            ]
          }
        ],
      },
      // 仪表图配置
      gaugeConfig: {
        color: [
          "#00d4ff",
          "#ff6b6b",
          "#4ecdc4",
          "#45b7d1"
        ],
        backgroundColor: "#ccc",
        colorMode: "solid",
        selectedColorScheme: "default",
        customColorSchemes: {
          "自定义1": [
            "#4eff00",
            "#f89063",
            "#e6a23e"
          ]
        },
        graphic: {
          type: "image",
          id: "background",
          style: {
            image: "",
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
          },
          z: -1,
        },
        title: {
          text: "仪表图",
          left: "center",
          top: "1%",
          textStyle: {
            fontSize: 16,
            fontWeight: "normal",
            fontStyle: "normal",
            fontFamily: "Arial",
          },
        },
        tooltip: {
          formatter: "{a} <br/>{b}: {c}",
        },
        cardStyle: {
          borderColor: "#000000",
          borderWidth: 1,
          borderStyle: "solid",
          borderRadius: 0,
        },
        series: [
          {
            name: "仪表盘",
            type: "gauge",
            center: ["50%", "60%"],
            radius: "75%",
            min: 0,
            max: 1000,
            splitNumber: 10,
            axisLine: {
              lineStyle: {
                width: 10,
                color: [
                  [0.3, "#67e0e3"],
                  [0.7, "#37a2da"],
                  [1, "#fd666d"]
                ]
              }
            },
            pointer: {
              itemStyle: {
                color: "auto"
              }
            },
            axisTick: {
              distance: -30,
              length: 8,
              lineStyle: {
                color: "#fff",
                width: 2
              }
            },
            splitLine: {
              distance: -30,
              length: 30,
              lineStyle: {
                color: "#fff",
                width: 4
              }
            },
            axisLabel: {
              color: "auto",
              distance: 40,
              fontSize: 12
            },
            detail: {
              valueAnimation: true,
              formatter: "{value}",
              color: "auto",
              fontSize: 30,
              offsetCenter: [0, "70%"]
            },
            data: [
              {
                value: 760,
                name: "你的得分"
              }
            ]
          }
        ],
      },
    },
  }),
  getters: {},
  actions: {
    // 重置为默认配置
    resetToDefault() {
      // 重置页面配置
      this.pageConfig = {
        basicConfig: {
          themeTitle: "可视化",
          themeCover: "",
          titleFont: {
            fontFamily: "Arial",
            fontSize: 40,
            fontWeight: "normal",
            fontStyle: "normal",
            textAlign: "center"
          },
        },
        globalStyle: {
          pageFonts: "",
          componentBorderRadius: "0px",
          componentSpacing: "5px",
        },
        pageLayout: {
          pageTitle: "show",
          pageBackgroundColor: "#edf2fa",
          gradientColor: "linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 100%)",
          colorMode: "solid",
          pageTopImg: "",
          pageBottomImg: "",
          pagePadding: "5px",
        },
      };

      // 重置图表配置为默认值
      this.chartConfig = {
        barConfig: {
          color: [
            "#409eff", "#f56c6c", "#e6a23c", "#5cb87a", "#909399",
            "#dcdfe6", "#409eff", "#f56c6c", "#e6a23c", "#5cb87a",
          ],
          backgroundColor: "#ccc",
          colorMode: "solid",
          selectedColorScheme: "default",
          customColorSchemes: {
            "自定义1": [
              "#4eff00", "#f89063", "#e6a23e", "#5cb87a", "#909399",
              "#dcdfe6", "#409eff", "#f56c6c"
            ]
          },
          graphic: {
            type: "image",
            id: "background",
            style: {
              image: "",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
            },
            z: -1,
          },
          legend: {
            x: "left",
            y: "top",
            top: "2%",
            orient: "horizontal", // 柱状图默认水平排列
            textStyle: {
              fontFamily: "Arial",
              fontStyle: "normal",
              color: "#000000",
              fontSize: 14,
              fontWeight: "normal",
            },
          },
          title: {
            text: "数据统计",
            left: "center",
            top: "1%",
            textStyle: {
              fontSize: 16,
              fontWeight: "normal",
              fontStyle: "normal",
              fontFamily: "Arial",
            },
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            top: "16%",
            containLabel: true,
            show: false,
            backgroundColor: "transparent",
          },
          cardStyle: {
            borderColor: "#000000",
            borderWidth: 1,
            borderStyle: "solid",
            borderRadius: 0,
          },
          xAxis: {
            type: "category",
            data: ["一月", "二月", "三月", "四月", "五月", "六月"],
            axisTick: {
              alignWithLabel: true,
            },
            splitLine: {
              show: true,
              interval: "auto",
              lineStyle: {
                color: "#000",
                width: 1,
                type: "solid",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#000",
                width: 2,
                type: "solid",
              },
            },
          },
          yAxis: {
            type: "value",
            splitLine: {
              show: true,
              interval: "auto",
              lineStyle: {
                color: "#000",
                width: 1,
                type: "solid",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#000",
                width: 2,
                type: "solid",
              },
            },
          },
          series: [
            {
              name: "销售额",
              type: "bar",
              barWidth: 24,
              data: [120, 200, 150, 80, 70, 110],
              itemStyle: {
                borderRadius: [5, 5, 0, 0],
              },
            },
          ],
        },
        pieConfig: {
          color: [
            "#409eff", "#f56c6c", "#e6a23c", "#5cb87a", "#909399",
            "#dcdfe6", "#409eff", "#f56c6c", "#e6a23c", "#5cb87a",
          ],
          backgroundColor: "#ccc",
          colorMode: "solid",
          selectedColorScheme: "default",
          customColorSchemes: {
            "自定义1": [
              "#4eff00", "#f89063", "#e6a23e", "#5cb87a", "#909399",
              "#dcdfe6", "#409eff", "#f56c6c"
            ]
          },
          graphic: {
            type: "image",
            id: "background",
            style: {
              image: "",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
            },
            z: -1,
          },
          legend: {
            x: "right",
            y: "center",
            top: "middle",
            orient: "vertical", // 饼图默认垂直排列
            textStyle: {
              fontFamily: "Arial",
              fontStyle: "normal",
              color: "#000000",
              fontSize: 14,
              fontWeight: "normal",
            },
          },
          title: {
            text: "数据统计",
            left: "center",
            top: "1%",
            textStyle: {
              fontSize: 16,
              fontWeight: "normal",
              fontStyle: "normal",
              fontFamily: "Arial",
            },
          },
          tooltip: {
            trigger: "item",
            formatter: "{a} <br/>{b}: {c} ({d}%)",
          },
          cardStyle: {
            borderColor: "#000000",
            borderWidth: 1,
            borderStyle: "solid",
            borderRadius: 0,
          },
          series: [
            {
              name: "数据分布",
              type: "pie",
              radius: ["30%", "50%"],
              center: ["30%", "50%"],
              data: [
                { value: 335, name: "直接访问" },
                { value: 310, name: "邮件营销" },
                { value: 234, name: "联盟广告" },
                { value: 135, name: "视频广告" },
                { value: 1548, name: "搜索引擎" }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              },
              label: {
                show: true,
                formatter: "{b}: {d}%"
              },
              labelLine: {
                show: true
              }
            },
          ],
        },
        barHorizontalConfig: {
          color: [
            "#409eff", "#f56c6c", "#e6a23c", "#5cb87a", "#909399",
            "#dcdfe6", "#409eff", "#f56c6c", "#e6a23c", "#5cb87a",
          ],
          backgroundColor: "#ccc",
          colorMode: "solid",
          selectedColorScheme: "default",
          customColorSchemes: {
            "自定义1": [
              "#4eff00", "#f89063", "#e6a23e", "#5cb87a", "#909399",
              "#dcdfe6", "#409eff", "#f56c6c"
            ]
          },
          graphic: {
            type: "image",
            id: "background",
            style: {
              image: "",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
            },
            z: -1,
          },
          legend: {
            x: "left",
            y: "top",
            top: "2%",
            orient: "horizontal", // 条形图默认水平排列
            textStyle: {
              fontFamily: "Arial",
              fontStyle: "normal",
              color: "#000000",
              fontSize: 14,
              fontWeight: "normal",
            },
          },
          title: {
            text: "数据统计",
            left: "center",
            top: "1%",
            textStyle: {
              fontSize: 16,
              fontWeight: "normal",
              fontStyle: "normal",
              fontFamily: "Arial",
            },
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          grid: {
            left: "15%",
            right: "4%",
            bottom: "3%",
            top: "16%",
            containLabel: true,
            show: false,
            backgroundColor: "transparent",
          },
          cardStyle: {
            borderColor: "#000000",
            borderWidth: 1,
            borderStyle: "solid",
            borderRadius: 0,
          },
          xAxis: {
            type: "value",
            splitLine: {
              show: true,
              interval: "auto",
              lineStyle: {
                color: "#000",
                width: 1,
                type: "solid",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#000",
                width: 1,
                type: "solid",
              },
            },
          },
          yAxis: {
            type: "category",
            data: ["一月", "二月", "三月", "四月", "五月", "六月"],
            splitLine: {
              show: false,
              lineStyle: {
                color: "#000",
                width: 1,
                type: "solid",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#000",
                width: 1,
                type: "solid",
              },
            },
          },
          series: [
            {
              name: "销量",
              type: "bar",
              data: [120, 200, 150, 80, 70, 110],
              barWidth: 24,
              itemStyle: {
                borderRadius: [0, 4, 4, 0],
              },
            },
          ],
        },
        lineConfig: {
          color: [
            "#409eff", "#f56c6c", "#e6a23c", "#5cb87a", "#909399",
            "#dcdfe6", "#409eff", "#f56c6c", "#e6a23c", "#5cb87a",
          ],
          backgroundColor: "#ccc",
          colorMode: "solid",
          selectedColorScheme: "default",
          customColorSchemes: {
            "自定义1": [
              "#4eff00", "#f89063", "#e6a23e", "#5cb87a", "#909399",
              "#dcdfe6", "#409eff", "#f56c6c"
            ]
          },
          graphic: {
            type: "image",
            id: "background",
            style: {
              image: "",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
            },
            z: -1,
          },
          legend: {
            x: "left",
            y: "top",
            top: "2%",
            orient: "horizontal", // 折线图默认水平排列
            textStyle: {
              fontFamily: "Arial",
              fontStyle: "normal",
              color: "#000000",
              fontSize: 14,
              fontWeight: "normal",
            },
          },
          title: {
            text: "数据统计",
            left: "center",
            top: "1%",
            textStyle: {
              fontSize: 16,
              fontWeight: "normal",
              fontStyle: "normal",
              fontFamily: "Arial",
            },
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            top: "16%",
            containLabel: true,
            show: false,
            backgroundColor: "transparent",
          },
          cardStyle: {
            borderColor: "#000000",
            borderWidth: 1,
            borderStyle: "solid",
            borderRadius: 0,
          },
          xAxis: {
            type: "category",
            data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
            axisTick: {
              alignWithLabel: true,
            },
            splitLine: {
              show: true,
              interval: "auto",
              lineStyle: {
                color: "#000",
                width: 1,
                type: "solid",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#000",
                width: 2,
                type: "solid",
              },
            },
          },
          yAxis: {
            type: "value",
            splitLine: {
              show: true,
              interval: "auto",
              lineStyle: {
                color: "#000",
                width: 1,
                type: "solid",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#000",
                width: 2,
                type: "solid",
              },
            },
          },
          series: [
            {
              name: "数据趋势",
              type: "line",
              data: [820, 932, 901, 934, 1290, 1330, 1320],
              smooth: true,
              lineStyle: {
                width: 3,
                color: "#409eff",
              },
              itemStyle: {
                color: "#409eff",
                borderWidth: 2,
                borderColor: "#fff",
              },
              areaStyle: {
                opacity: 0.3,
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0, color: '#409eff'
                  }, {
                    offset: 1, color: 'rgba(64, 158, 255, 0.1)'
                  }]
                }
              },
            },
          ],
        },
        funnelConfig: {
          color: [
            "#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de",
            "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"
          ],
          backgroundColor: "#ccc",
          colorMode: "solid",
          selectedColorScheme: "default",
          customColorSchemes: {
            "自定义1": [
              "#4eff00", "#f89063", "#e6a23e", "#5cb87a", "#909399"
            ]
          },
          graphic: {
            type: "image",
            id: "background",
            style: {
              image: "",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
            },
            z: -1,
          },
          legend: {
            x: "right",
            y: "center",
            top: "middle",
            orient: "vertical", // 漏斗图默认垂直排列
            textStyle: {
              fontFamily: "Arial",
              fontStyle: "normal",
              color: "#000000",
              fontSize: 14,
              fontWeight: "normal",
            },
          },
          title: {
            text: "数据图表",
            left: "center",
            top: "1%",
            textStyle: {
              fontSize: 16,
              fontWeight: "normal",
              fontStyle: "normal",
              fontFamily: "Arial",
            },
          },
          tooltip: {
            trigger: "item",
            formatter: "{a} <br/>{b}: {c} ({d}%)",
          },
          cardStyle: {
            borderColor: "#000000",
            borderWidth: 1,
            borderStyle: "solid",
            borderRadius: 0,
          },
          series: [
            {
              name: "漏斗图",
              type: "funnel",
              left: "10%",
              top: "10%",
              width: "60%",
              height: "80%",
              min: 0,
              max: 100,
              minSize: "0%",
              maxSize: "100%",
              sort: "descending",
              gap: 2,
              label: {
                show: true,
                position: "inside",
                formatter: "{b}: {c}%"
              },
              labelLine: {
                length: 10,
                lineStyle: {
                  width: 1,
                  type: "solid"
                }
              },
              itemStyle: {
                borderColor: "#fff",
                borderWidth: 1
              },
              emphasis: {
                label: {
                  fontSize: 20
                }
              },
              data: [
                { value: 100, name: "产品浏览" },
                { value: 80, name: "点击咨询" },
                { value: 60, name: "访客咨询" },
                { value: 40, name: "电话咨询" },
                { value: 20, name: "完成交易" }
              ]
            }
          ],
        },
        radarConfig: {
          color: [
            "#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57",
            "#ff9ff3", "#54a0ff"
          ],
          backgroundColor: "#ccc",
          colorMode: "solid",
          selectedColorScheme: "default",
          customColorSchemes: {
            "自定义1": [
              "#4eff00", "#f89063", "#e6a23e", "#5cb87a"
            ]
          },
          graphic: {
            type: "image",
            id: "background",
            style: {
              image: "",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
            },
            z: -1,
          },
          legend: {
            x: "center",
            y: "bottom",
            top: "90%",
            orient: "vertical", // 雷达图默认垂直排列
            textStyle: {
              fontFamily: "Arial",
              fontStyle: "normal",
              color: "#000000",
              fontSize: 14,
              fontWeight: "normal",
            },
          },
          title: {
            text: "雷达图",
            left: "center",
            top: "1%",
            textStyle: {
              fontSize: 16,
              fontWeight: "normal",
              fontStyle: "normal",
              fontFamily: "Arial",
            },
          },
          tooltip: {
            trigger: "item",
          },
          cardStyle: {
            borderColor: "#000000",
            borderWidth: 1,
            borderStyle: "solid",
            borderRadius: 0,
          },
          radar: {
            indicator: [
              { name: "销售一", max: 10000 },
              { name: "销售二", max: 10000 },
              { name: "销售三", max: 10000 },
              { name: "销售四", max: 10000 },
              { name: "销售五", max: 10000 },
              { name: "销售六", max: 10000 }
            ],
            center: ["50%", "50%"],
            radius: "50%",
            axisName: {
              color: "#000",
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                color: "#ccc"
              }
            },
            splitArea: {
              show: true
            },
            axisLine: {
              lineStyle: {
                color: "#ccc"
              }
            }
          },
          series: [
            {
              name: "数据一",
              type: "radar",
              data: [
                {
                  value: [8000, 7000, 6000, 5000, 4000, 3000],
                  name: "数据一",
                  areaStyle: {
                    opacity: 0.3
                  }
                },
                {
                  value: [6000, 8000, 7000, 6000, 5000, 4000],
                  name: "数据二",
                  areaStyle: {
                    opacity: 0.3
                  }
                }
              ]
            }
          ],
        },
        gaugeConfig: {
          color: [
            "#00d4ff", "#ff6b6b", "#4ecdc4", "#45b7d1"
          ],
          backgroundColor: "#ccc",
          colorMode: "solid",
          selectedColorScheme: "default",
          customColorSchemes: {
            "自定义1": [
              "#4eff00", "#f89063", "#e6a23e"
            ]
          },
          graphic: {
            type: "image",
            id: "background",
            style: {
              image: "",
              left: 0,
              top: 0,
              width: "100%",
              height: "100%",
            },
            z: -1,
          },
          title: {
            text: "仪表图",
            left: "center",
            top: "1%",
            textStyle: {
              fontSize: 16,
              fontWeight: "normal",
              fontStyle: "normal",
              fontFamily: "Arial",
            },
          },
          tooltip: {
            formatter: "{a} <br/>{b}: {c}",
          },
          cardStyle: {
            borderColor: "#000000",
            borderWidth: 1,
            borderStyle: "solid",
            borderRadius: 0,
          },
          series: [
            {
              name: "仪表盘",
              type: "gauge",
              center: ["50%", "60%"],
              radius: "75%",
              min: 0,
              max: 1000,
              splitNumber: 10,
              axisLine: {
                lineStyle: {
                  width: 10,
                  color: [
                    [0.3, "#67e0e3"],
                    [0.7, "#37a2da"],
                    [1, "#fd666d"]
                  ]
                }
              },
              pointer: {
                itemStyle: {
                  color: "auto"
                }
              },
              axisTick: {
                distance: -30,
                length: 8,
                lineStyle: {
                  color: "#fff",
                  width: 2
                }
              },
              splitLine: {
                distance: -30,
                length: 30,
                lineStyle: {
                  color: "#fff",
                  width: 4
                }
              },
              axisLabel: {
                color: "auto",
                distance: 40,
                fontSize: 12
              },
              detail: {
                valueAnimation: true,
                formatter: "{value}",
                color: "auto",
                fontSize: 30,
                offsetCenter: [0, "70%"]
              },
              data: [
                {
                  value: 760,
                  name: "你的得分"
                }
              ]
            }
          ],
        }
      };

      console.log('已重置为默认配置');
    }
  },
  // 新增持久化配置
  persist: [
    {
      key: "VISUAL_STORE", // 存储的key
      storage: localStorage, // 选择存储介质
      paths: ["pageConfig"], // 指定要持久化的state字段
    },
  ],
});

export const useVisualStoreWithOut = () => {
  return useVisualStore(store);
};
