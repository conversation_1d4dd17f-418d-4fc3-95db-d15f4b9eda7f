<template>
  <div class="sidebar" style="width: 250px">
    <div class="logo">
      <span class="logo-text">后台系统</span>
    </div>
    <el-menu
      :router="true"
      :default-active="activeMenu"
      :default-openeds="defaultOpeneds"
      class="el-menu-vertical-demo"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409eff"
      :collapse="isCollapse"
      @open="handleOpen"
      @close="handleClose"
    >
      <SidebarItem v-for="route in routes" :key="route.path" :route="route" basePath="/" />
    </el-menu>
  </div>
</template>

<script setup>
import {ref, computed, watch, provide} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import SidebarItem from '@/Layouts/components/SidebarItem.vue';
const route = useRoute();
const router = useRouter();

const routes = computed(() => {
  const baseRoute = router.options.routes.find(r => r.path === '/');
  return baseRoute?.children?.filter(r => r.meta?.menu || r.meta?.grouping) || [];
});

const activeMenu = computed(() => {
  if (route.meta && route.meta.isActive) {
    return route.meta.isActive;
  }
  const matched = route.matched;
  for (let i = matched.length - 1; i >= 0; i--) {
    if (matched[i].meta && matched[i].meta.menu) {
      return matched[i].path;
    }
  }
  return route.path;
});

const defaultOpeneds = computed(() => {
  const segments = route.path.split('/').filter(Boolean);
  const openeds = [];
  for (let i = 1; i < segments.length; i++) {
    openeds.push('/' + segments.slice(0, i).join('/'));
  }
  return openeds;
});

const isCollapse = ref(false);
const handleOpen = (key, keyPath) => {
  console.log('Menu opened:', key, keyPath);
};
const handleClose = (key, keyPath) => {
  console.log('Menu closed:', key, keyPath);
};

watch(
  () => route.path,
  newPath => {
    localStorage.setItem('activeMenu', newPath);
  }
);

provide('activeMenu', activeMenu);
</script>

<style lang="scss" scoped>
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  background-color: #304156;
  transition: all 0.3s;
  z-index: 100;
  display: flex;
  flex-direction: column;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
    background-color: #2c3e50;
    border-bottom: 1px solid #3a526b;
    margin-bottom: 10px;

    .logo-text {
      color: #fff;
      font-size: 18px;
      font-weight: bold;
    }
  }

  :deep(.el-menu) {
    border-right: none;
    flex: 1;

    .el-sub-menu__title,
    .el-menu-item {
      height: 50px;
      line-height: 50px;
      font-size: 15px;
    }

    .el-sub-menu__title:hover,
    .el-menu-item:hover {
      background-color: #2c3e50 !important;
    }

    .el-icon {
      color: #bfcbd9;
      font-size: 18px;
      width: 30px;
      text-align: center;
    }

    .el-menu-item.is-active {
      background-color: #2c3e50 !important;
      border-right: 3px solid #409eff;
    }

    .el-sub-menu.is-active > .el-sub-menu__title {
      color: #409eff;
    }
  }

  .el-menu--collapse {
    width: 80px;
    background-color: #304156;
  }
}
.el-menu-vertical-demo {
  position: relative;
}
</style>
