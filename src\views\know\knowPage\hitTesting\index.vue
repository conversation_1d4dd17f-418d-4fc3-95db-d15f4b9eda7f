<template>
  <PageHeader title="命中测试">
    <template #page-header-back>
      <el-icon class="header-back-icon" @click="handleClose"><ArrowLeftBold /></el-icon>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <div class="test-management">
        <!-- 左侧面板 -->
        <div class="left-panel">
          <div class="section-title">
            <span>
              测试范围：共
              <span class="section-title-num">{{ knowBaseNum }}</span>
              个知识库
            </span>
            <el-icon @click="addKnowBaseClick"><Plus /></el-icon>
          </div>
          <div class="test-box">
            <el-input v-model="query" placeholder="请输入内容" class="search-input" type="textarea" :rows="6" />
            <el-button class="preview-btn" @click="testPreview">测试预览</el-button>
          </div>

          <div class="section-title">测试历史</div>
          <el-table :data="historyData" style="width: 100%">
            <el-table-column prop="query" label="Query" />
            <el-table-column prop="time" label="时间" />
          </el-table>
        </div>

        <!-- 右侧预览面板 -->
        <div class="right-panel">
          <div class="section-title">测试预览</div>

          <!-- 遍历预览卡片 -->
          <div v-for="(card, index) in previewCards" :key="index" class="preview-card" :style="{border: '1px solid #E8E8E8'}">
            <div class="card-header">
              <el-tag type="info">自动切片{{ index + 1 }}</el-tag>
              <el-tag type="warning" style="margin-left: 15px">命中切片</el-tag>
              <div class="score">
                <span>分值：</span>
                <el-progress :percentage="card.score * 100" :show-text="false" :stroke-width="6" style="width: 100px" />
                <span class="score-value">{{ card.score.toFixed(3) }}</span>
              </div>
            </div>
            <div class="data-preview">{{ card.content }}</div>
            <div class="source">{{ card.source }}</div>
          </div>
          <el-empty description="暂无数据" v-if="previewCards.length == 0" />
        </div>
      </div>
      <div class="content-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </div>
  </el-card>
  <AddKnowBase ref="addKnowBaseRef" @handleClose="handleClose" @updateKbCount="knowBaseNum = $event"></AddKnowBase>
</template>

<script lang="ts" setup>
import {ref, onMounted, watch, toRaw} from 'vue';
import {Plus, ArrowLeftBold} from '@element-plus/icons-vue';
import * as api from '@/api/know/knowPage/hitTesting/index';
import AddKnowBase from './components/AddKnowBase.vue';
import {useKnowStore} from '@/store/modules/know';
import {useRouter} from 'vue-router';
import PageHeader from '@/components/PageHeader.vue';

const router = useRouter();
const knowStore = useKnowStore();

const addKnowBaseRef = ref(null);
let knowBaseNum = ref(0);
const historyData = ref([{query: '产量', time: '2025-02-24 07:35:12'}]);
const knowledgeBaseId = ref(knowStore.knowParamsForm.id || '');
const query = ref('');

watch(
  () => addKnowBaseRef.value?.addedIds,
  val => {
    knowBaseNum.value = val.length;
  },
  {deep: true}
);

// 预览卡片数据
const previewCards = ref([]);

const addKnowBaseClick = () => {
  addKnowBaseRef.value.open();
};

// 测试预览
const testPreview = async () => {
  try {
    const addedIds = addKnowBaseRef.value.addedIds || [];
    console.log('testPreview 传入的 addedIds:', addedIds);

    const res = await api.chunkHitTest({
      knowledgeBaseId: knowledgeBaseId.value,
      query: query.value,
      addedIds, // 如接口需要
    });
    previewCards.value = res[0]?.segment?.sentences || [];
    previewCards.value = previewCards.value.filter(card => card.score >= 0.4).sort((a, b) => b.score - a.score);
  } catch (error) {
    console.error('测试预览失败:', error);
  }
};

// 关闭对话框
const handleClose = () => {
  router.back();
};

// 确认操作
const handleConfirm = () => {
  // 在这里添加确认操作的逻辑
};
</script>

<style scoped lang="scss">
.test-management {
  display: flex;
  height: 700px;
  .left-panel {
    width: 400px;
    padding: 20px;
    border-right: 1px solid #e8e8e8;
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
      padding: 7px;
      background: #f3f5f9;
      border-radius: 10px;
      .section-title-num {
        color: #409eff;
      }
      .el-icon {
        color: #409eff;
        cursor: pointer;
      }
    }
    .search-input {
      margin-bottom: 20px;
    }

    .test-box {
      position: relative;
      width: 100%;
      height: 200px;
      border: 1px solid #999;
      border-radius: 10px;
      margin-bottom: 15px;
      :deep(.el-textarea__inner) {
        background: transparent;
        border-radius: 0;
        box-shadow: none;
      }
      :deep(el-input wrapper.is-focus) {
        box-shadow: none;
      }
      :deep(.el-textarea__inner) {
        resize: none;
      }
      .preview-btn {
        position: absolute;
        bottom: -7px;
        right: 10px;
        width: 90px;
        margin-bottom: 20px;
        margin-top: 20px;
      }
    }
  }
  .right-panel {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    .preview-card {
      background-color: #fff;
      border-radius: 8px;
      margin-bottom: 20px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        .score {
          margin-left: auto;
          display: flex;
          align-items: center;
          .score-value {
            margin-left: 10px;
            font-weight: bold;
          }
        }
      }
      .data-preview {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 10px;
        overflow-y: auto;
        max-height: 200px;
      }
      .source {
        text-align: left;
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>
