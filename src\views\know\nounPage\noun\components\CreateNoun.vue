<template>
  <el-dialog v-model="dialogVisible" title="创建名词" width="30%" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" size="default">
      <!-- <el-form-item label="名词分组" prop="groupName">
        <span>{{ form.groupName }}</span>
      </el-form-item> -->

      <el-form-item label="业务名词" prop="businessName">
        <el-input v-model="form.businessName" placeholder="请输入业务名词"></el-input>
      </el-form-item>

      <el-form-item label="名词类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择名词类型">
          <el-option v-for="item in nounTypeBtnList" :key="item.code" :label="item.message" :value="item.code" v-show="item.code != '-1'"></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {ref, reactive} from 'vue';
import {ElMessage} from 'element-plus';
import * as api from '@/api/know/noun/index';

const emit = defineEmits(['resetPagination']);
const props = defineProps({
  activeTab: {
    type: String,
    required: true,
    default: '',
  },
  nounTypeBtnList: <any>{
    type: Array,
    required: true,
    default: () => [],
  },
});

const dialogVisible = ref(false);
const formRef = ref();
const dialogType = ref('');

const form = reactive({
  // 隐式类型推断
  groupName: '',
  businessName: '',
  type: undefined,
  groupId: '',
  id: '',
  synonym: '',
  definitions: '',
});

const rules = reactive({
  // 简化验证规则
  businessName: [
    {
      required: true,
      message: '请输入业务名词',
      trigger: ['blur', 'change'],
    },
  ],
  type: [
    {
      required: true,
      message: '请选择名词类型',
      trigger: 'change',
    },
  ],
});

const openDialog = (id, type, row) => {
  // 移除参数类型声明
  dialogVisible.value = true;
  dialogType.value = type;
  form.groupId = id;
  form.groupName = props.activeTab;

  if (type === 'edit') {
    Object.keys(form).forEach(key => {
      if (row && row.hasOwnProperty(key)) {
        form[key] = row[key];
      }
    });
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const handleSubmit = async () => {
  formRef.value?.validate(async valid => {
    if (!valid) {
      ElMessage.error('请填写完整信息');
      return;
    }

    try {
      if (dialogType.value === 'add') {
        await api.definitionsCreate(form);
      } else {
        await api.definitionsUpdate(form.id,{definition:form.type,businessName:form.businessName});
      }
      emit('resetPagination');
      dialogVisible.value = false;
      ElMessage.success('提交成功');
      resetForm();
    } catch (error) {
      console.error('操作失败:', error);
    }
  });
};

const resetForm = () => {
  formRef.value?.resetFields();
  Object.keys(form).forEach(key => {
    form[key] = key === 'type' ? undefined : '';
  });
};

defineExpose({openDialog});
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
