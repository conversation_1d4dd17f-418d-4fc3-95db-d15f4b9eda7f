<template>
  <!-- <component :is="BarConfig1" :componentName="componentName"></component> -->
  <BarConfig :componentName="componentName"></BarConfig>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import BarConfig from './BarConfig/index.vue';
import LineConfig from './LineConfig/index.vue';

const props = defineProps({
  componentName: {
    type: String,
    required: true,
    default: 'barConfig',
  },
});

const BarConfig1 = computed(() => {
  switch (props.componentName) {
    case 'barConfig': return BarConfig;
    case 'barHorizontalConfig': return BarConfig; // 条形图使用相同配置
    case 'lineConfig': return BarConfig;
    case 'pieConfig': return BarConfig; // 饼状图隐藏坐标轴配置
    case 'funnelConfig': return BarConfig; // 漏斗图隐藏坐标轴配置
    case 'radarConfig': return BarConfig; // 雷达图隐藏坐标轴配置
    case 'gaugeConfig': return BarConfig; // 仪表图隐藏坐标轴和图例配置
    default: return null;
  }
});
console.log('props.componentName', props.componentName);

</script>
<style>

</style>