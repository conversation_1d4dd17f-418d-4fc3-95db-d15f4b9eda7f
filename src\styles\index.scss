@use './var.css';
@use './FormCreate/index.scss';
@use './theme.scss';
@use 'element-plus/theme-chalk/dark/css-vars.css';
@use './common.scss';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow: 0 0 10px var(--el-color-primary), 0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}
// 按钮
.el-button--primary {
  --el-button-bg-color: #2744d6;
  --el-button-border-color: #2744d6;
  --el-button-hover-bg-color: #0a31f1;
}
// 输入框
.el-input__wrapper {
  // background: #f5f7fa;
  border-radius: 10px;
}
// 下拉
.el-select__wrapper {
  // background: #f5f7fa;
  border-radius: 10px;
}
// 复选框
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #2744d6;
  border-color: #2744d6;
}
.el-dialog {
  border-radius: 10px;
}

// 步骤条选中颜色
.el-step {
  --el-color-primary: #2744d6;
}

// tabs 标签页选中颜色
.el-tabs {
  --el-color-primary: #2744d6;
}
