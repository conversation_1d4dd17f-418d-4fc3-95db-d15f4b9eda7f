<template>
  <el-dialog :title="dialogType === 'add' ? '新增工具' : '编辑工具'" v-model="dialogVisible" width="800px">
    <el-form :model="formData" label-width="160px" :rules="rules" ref="formRef">
      <el-form-item label="工具中文名称（全称）" prop="toolChName">
        <el-input v-model="formData.toolChName" />
      </el-form-item>
      <el-form-item label="工具中文名称（简称）" prop="toolAbbreviationChName">
        <el-input v-model="formData.toolAbbreviationChName" />
      </el-form-item>
      <el-form-item label="工具英文名称（全称）" prop="toolEnName">
        <el-input v-model="formData.toolEnName" />
      </el-form-item>
      <el-form-item label="工具英文（简称）" prop="toolAbbreviationEnName">
        <el-input v-model="formData.toolAbbreviationEnName" />
      </el-form-item>
      <el-form-item label="工具描述" prop="toolDesc">
        <el-input type="textarea" v-model="formData.toolDesc" />
      </el-form-item>
      <el-form-item label="必须计算字段" prop="toolRequiredField" :rules="rules.toolRequiredField" required>
        <div v-for="(field, index) in formData.toolRequiredField" :key="index" class="field-container">
          <el-input v-model="formData.toolRequiredField[index]" style="flex: 1; margin-right: 10px" />
          <el-button v-if="index === formData.toolRequiredField.length - 1" @click="addField('toolRequiredField')">+</el-button>
          <el-button v-if="formData.toolRequiredField.length > 1" @click="removeField('toolRequiredField', index)">-</el-button>
        </div>
      </el-form-item>
      <el-form-item label="计算公式" prop="toolFormula">
        <el-input type="textarea" v-model="formData.toolFormula" />
      </el-form-item>
      <el-form-item label="计算逻辑" prop="toolLogic" :rules="rules.toolLogic" required>
        <div v-for="(logic, index) in formData.toolLogic" :key="index" class="field-container">
          <el-input v-model="formData.toolLogic[index]" style="flex: 1; margin-right: 10px" />
          <el-button v-if="index === formData.toolLogic.length - 1" @click="addField('toolLogic')">+</el-button>
          <el-button v-if="formData.toolLogic.length > 1" @click="removeField('toolLogic', index)">-</el-button>
        </div>
      </el-form-item>
      <el-form-item label="工具使用目的" prop="toolPurpose">
        <el-input type="textarea" v-model="formData.toolPurpose" />
      </el-form-item>
      <el-form-item label="常用应用场景" prop="toolScene">
        <el-input type="textarea" v-model="formData.toolScene" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确认</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, reactive} from 'vue';
import {ElMessage} from 'element-plus';
import * as api from '@/api/tool/index';

const props = defineProps({
  dialogType: {
    type: String,
    default: 'add',
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['resetPagination']);

const formRef = ref();
const dialogVisible = ref(false);
const formData = reactive({
  id: '',
  toolChName: '',
  toolAbbreviationChName: '',
  toolEnName: '',
  toolAbbreviationEnName: '',
  toolDesc: '',
  toolRequiredField: [''], // 改为数组
  toolFormula: '',
  toolLogic: [''], // 改为数组
  toolPurpose: '',
  toolScene: '',
});

const addField = fieldName => {
  formData[fieldName].push('');
};

const removeField = (fieldName, index) => {
  formData[fieldName].splice(index, 1);
};

const rules = {
  toolChName: [{required: true, message: '必填项', trigger: 'blur'}],
  toolAbbreviationChName: [{required: true, message: '必填项', trigger: 'blur'}],
  toolEnName: [{required: true, message: '必填项', trigger: 'blur'}],
  toolAbbreviationEnName: [{required: true, message: '必填项', trigger: 'blur'}],
  toolDesc: [{required: true, message: '必填项', trigger: 'blur'}],
  toolRequiredField: {
    trigger: 'submit',
    validator: (_: any, value: any, callback: any) => {
      if (value.some((item: any) => !item)) {
        callback(new Error('所有字段必须填写'));
      } else {
        callback();
      }
    },
  },
  toolLogic: {
    trigger: 'submit',
    validator: (_: any, value: any, callback: any) => {
      if (value.some((item: any) => !item)) {
        callback(new Error('所有逻辑必须填写'));
      } else {
        callback();
      }
    },
  },
  toolFormula: [{required: true, message: '必填项', trigger: 'blur'}],
  toolScene: [
    {
      required: true,
      message: '必填项',
      trigger: 'blur',
    },
  ],
  toolPurpose: [
    {
      required: true,
      message: '必填项',
      trigger: 'blur',
    },
  ],
};

// 修改submitForm方法
const submitForm = async () => {
  try {
    await formRef.value.validate();

    const requestData = {
      ...formData,
      toolRequiredField: formData.toolRequiredField.filter(Boolean),
      toolLogic: formData.toolLogic.filter(Boolean),
    };

    if (props.dialogType === 'add') {
      await api.configCreate(requestData);
      ElMessage.success('添加成功');
    } else {
      await api.configUpdate({
        ...requestData,
      });
      ElMessage.success('修改成功');
    }
    emit('resetPagination');
    // 重置表单
    formRef.value.resetFields();
    Object.keys(formData).forEach(key => {
      formData[key] = '';
    });
    dialogVisible.value = false;
  } catch (err) {
    console.error('表单验证失败');
  }
};

const openDialog = (type: string, row?: any) => {
  dialogVisible.value = true;
  if (type === 'edit' && row) {
    Object.keys(formData).forEach(key => {
      // 特殊处理数组字段
      if (['toolRequiredField', 'toolLogic'].includes(key)) {
        formData[key] = row[key]?.length ? [...row[key]] : [''];
      } else {
        formData[key] = row[key] || '';
      }
    });
  } else {
    // 重置表单时保持数组结构
    Object.keys(formData).forEach(key => {
      formData[key] = ['toolRequiredField', 'toolLogic'].includes(key) ? [''] : '';
    });
  }
};

defineExpose({dialogVisible, openDialog});
</script>

<style scoped>
.field-container {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
