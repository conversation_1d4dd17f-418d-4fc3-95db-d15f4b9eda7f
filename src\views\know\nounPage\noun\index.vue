<template>
  <PageHeader title="名词定义">
    <template #default>
      <div class="header">
        <!-- 注释名词定义分组相关代码 -->
        <!-- <div class="tabs">
          <el-tabs v-model="activeTab" @tab-click="tabChange">
            <el-tab-pane v-for="item in groupList" :key="item.name" :label="item.name" :name="item.name" />
          </el-tabs>
          <el-icon class="header_icon" @click="createNounGroupClick"><CirclePlusFilled /></el-icon>
          <el-icon class="header_icon" @click="setPriorityClick"><Tools /></el-icon>
        </div> -->
        <div></div>
        <div class="tools">
          <el-input v-model="searchText" placeholder="请输入搜索内容" :prefix-icon="Search" @keydown.enter="getTableData" />
          <el-button @click="sift = !sift">
            <el-icon><Filter /></el-icon>
            筛选
          </el-button>
          <el-button type="primary" @click="createTerm">创建名词</el-button>
        </div>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <Transition name="fade-slide">
        <div class="filter-bar" v-show="sift">
          <el-form :inline="true">
            <el-form-item label="名词类型">
              <el-button v-for="item in nounTypeBtnList" :key="item.code" :type="item.code == nounTypeBtnActive ? 'primary' : 'default'" @click="handleTermType(item.code)">
                {{ item.message }}
              </el-button>
            </el-form-item>
            <el-form-item label="创建者">
              <el-select v-model="termTypeFilter" class="filter-item" @change="handleCreatedBy" clearable>
                <el-option v-for="item in termTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </Transition>
      <el-table :data="tableData" style="width: 100%" max-height="520px">
        <el-table-column prop="businessName" label="业务名词" width="120" />
        <el-table-column prop="synonyms" label="同义词" width="190">
          <template #default="scope">
            <div class="definition-text" :title="scope.row.synonyms">
              <span class="text-content">{{ scope.row.synonyms }}</span>
              <el-icon class="edit-icon" @click="openEditSynonym(scope.row)"><EditPen /></el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="termDefinitions" label="名词定义">
          <template #default="scope">
            <div class="definition-text" :title="scope.row.termDefinitions">
              <span class="text-content">{{ scope.row.termDefinitions }}</span>
              <el-icon class="edit-icon" @click="addNounDefinition(scope.row)"><EditPen /></el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="名词类型" width="120">
          <template #default="scope">
            <el-tag type="info" :style="{background: getTagType(scope.row.type), color: '#fff'}" disable-transitions>{{ getTagTypeText(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" width="120" />
        <el-table-column label="创建时间" width="180">
          <template #default="scope">
            {{ dayjs(scope.row.createTime).format('YYYY/MM/DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <div class="operation">
              <el-button link type="danger" @click="deleteTerm(scope.row.id)">删除</el-button>
              <el-button link type="primary" @click="editTerm(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="content-footer">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </div>
  </el-card>
  <CreateNoun ref="createNounRef" :activeTab="activeTab" :nounTypeBtnList="nounTypeBtnList" @resetPagination="resetPagination" />
  <EditSynonym ref="editSynonymRef" @resetPagination="resetPagination" />
  <CreateNounGroup ref="createNounGroupRef" @getGroupData="getGroupData" />
  <SetPriority ref="setPriorityRef" @getGroupData="getGroupData" />
</template>

<script lang="ts" setup>
import {ref, onMounted} from 'vue';
import dayjs from 'dayjs';
import {Search, CirclePlusFilled, Tools, EditPen, Filter} from '@element-plus/icons-vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import * as api from '@/api/know/noun/index';
import CreateNoun from './components/CreateNoun.vue';
import CreateNounGroup from './components/CreateNounGroup.vue';
import SetPriority from './components/SetPriority.vue';
import EditSynonym from './components/EditSynonym.vue';
import router from '@/router';
import {useKnowStore} from '@/store/modules/know';
import Relationship from './components/Relationship.vue';
import PageHeader from '@/components/PageHeader.vue';

const knowStore = useKnowStore();

const activeTab = ref<any>('');
const searchText = ref<any>('');
const termTypeFilter = ref<any>('');
const sift = ref<any>(false);
const createNounRef = ref<any>(null);
const editSynonymRef = ref<any>(null);
const createNounGroupRef = ref<any>(null);
const setPriorityRef = ref<any>(null);

const groupList = ref<any[]>([]);
const nounTypeBtnList = ref<any[]>([]);
const termTypeOptions = ref<any[]>([
  {label: '小明', value: '小明'},
  {label: '小美', value: '小美'},
]);
const nounTypeBtnActive = ref('-1');
const tableData = ref<any[]>([]);

const pagination = ref<any>({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

onMounted(() => {
  // getGroupData();
  getNounTypeBtnList();
  getTableData();
});
const handleViewType = () => {
  getGroupData();
};
const getNounTypeBtnList = async () => {
  try {
    const res = await api.definitionsTypeList();
    nounTypeBtnList.value = res;
    nounTypeBtnList.value.unshift({message: '不限', code: '-1'});
  } catch (error) {}
};

const resetPagination = () => {
  pagination.value.currentPage = 1;
  pagination.value.pageSize = 10;
  getTableData();
};

const getGroupData = async () => {
  try {
    groupList.value = [];
    const res = await api.groupPage({pageSize: '100', pageNo: '1'});
    groupList.value = res.list.sort((a: any, b: any) => a.priority - b.priority);
    activeTab.value = res.list.length ? res.list[0].name : '';
    getTableData();
  } catch (error) {
    console.log('error', error);
  }
};

const getTableData = async () => {
  try {
    const params = {
      pageNo: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
      groupName: activeTab.value,
      businessName: searchText.value,
      type: nounTypeBtnActive.value,
      creatorName: termTypeFilter.value,
    };
    const res = await api.definitionsPage(params);
    tableData.value = res.list || [];
    pagination.value.total = res.total || 0;
  } catch (error) {
    console.log('error:', error);
  }
};

const tabChange = (tab: any) => {
  resetPagination();
  activeTab.value = tab.props.name;
  getTableData();
};

const getTagType = (type: any) => {
  if (type === 1) return '#96F202';
  if (type === 2) return '#155EFE';
  if (type === 3) return '#f8b160';
  if (type === 4) return '#81D3F8';
  return '#F4F4F5';
};

const getTagTypeText = (type: any) => {
  if (type === 1) return '指标';
  if (type === 2) return '维度';
  if (type === 3) return '统计周期';
  if (type === 4) return '指标集合';
  return '';
};

const addNounDefinition = (row: any) => {
  // const currentGroup = groupList.value.find(item => item.name === activeTab.value);
  // if (currentGroup) {
  router.push({name: 'AddPriority', query: {id: row.id}});
  knowStore.setNoumParamsForm({
    ...row,
    // groupId: currentGroup.id,
  });
  // }
};
const openEditSynonym = (row: any) => {
  editSynonymRef.value.open(row);
};

const createNounGroupClick = () => {
  createNounGroupRef.value.openDialog();
};

const setPriorityClick = () => {
  setPriorityRef.value.openDialog();
};

const handleTermType = (val: any) => {
  resetPagination();
  nounTypeBtnActive.value = val;
  getTableData();
};

const handleCreatedBy = () => {
  resetPagination();
  getTableData();
};

const deleteTerm = (id: any) => {
  ElMessageBox.confirm('确定要删除这个术语吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      api
        .definitionsDelete({id})
        .then(() => {
          ElMessage.success('删除成功!');
          getTableData();
        })
        .catch(console.error);
    })
    .catch(() => {});
};

const createTerm = () => {
  // const currentGroup = groupList.value.find(item => item.name === activeTab.value);
  // if (currentGroup) {
  // createNounRef.value.openDialog(currentGroup.id, 'add');
  // }
  createNounRef.value.openDialog('', 'add');
};

const editTerm = (row: any) => {
  // const currentGroup = groupList.value.find(item => item.name === activeTab.value);
  // if (currentGroup) {
  //   createNounRef.value.openDialog(currentGroup.id, 'edit', row);
  // }
  createNounRef.value.openDialog('', 'edit', row);
};
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  height: 100%;
}

// 头部区域样式
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tabs {
    flex: 1;
    margin-left: 20px;
    display: flex;
    align-items: center;

    .el-tabs {
      width: fit-content;
    }

    .header_icon {
      font-size: 25px;
      margin-left: 30px;
      color: #606266;
      cursor: pointer;
    }
  }

  .tools {
    display: flex;
    align-items: center;
    gap: 15px;

    .el-select {
      width: 200px;
    }
    img {
      width: 25px;
      cursor: pointer;
    }
  }
}

// 筛选栏样式
.filter-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .filter-item {
    width: 200px;
    margin-right: 10px;
  }

  .search-input {
    flex: 1;
    margin: 0 10px;
  }

  .el-form-item {
    margin-bottom: 0;
  }
}

// 表格样式
.el-table {
  flex: 1;
  margin-bottom: 20px;

  .operation {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }
}

// 术语定义样式
.term-definition {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

// 过渡动画样式
:deep(.fade-slide-enter-active),
:deep(.fade-slide-leave-active) {
  transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
}

:deep(.fade-slide-enter-from),
:deep(.fade-slide-leave-to) {
  opacity: 0;
  transform: translateY(-10px);
}

// 多行省略样式
.definition-text {
  display: flex;
  justify-content: space-between;
  align-items: end;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
  max-height: 3.5em;
  word-break: break-word;

  .text-content {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 8px;
  }

  .edit-icon {
    flex-shrink: 0;
    margin-left: auto;
    font-size: 16px;
    color: #909399;

    &:hover {
      color: #409eff;
    }
  }
}
</style>
