<template>
  <el-dialog v-model="showDialog" title="批量导入" width="35%" :before-close="handleClose">
    <div class="upload-container">
      <el-upload :file-list="fileList"
        class="upload-area"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-exceed="handleExceed"
        :on-error="handleError"
        :before-upload="beforeUpload"
        :limit="1"
        accept=".csv, .xlsx, .xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          <span>点击或拖拽文件到此区域上传</span>
          <div class="upload-tip">
            <el-icon><info-filled /></el-icon>
            文件仅支持 .csv格式，大小不超过10MB
          </div>
        </div>
      </el-upload>

      <div class="template-download">
        <el-button type="primary" link @click="downloadTemplate">
          <el-icon><download /></el-icon>
          下载导入模板
        </el-button>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="isUploading">取消</el-button>
        <el-button type="primary" @click="handleImport" :loading="isUploading" :disabled="fileList.length === 0">
          {{ isUploading ? '导入中...' : '开始导入' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, defineEmits} from 'vue';
import {UploadFilled, InfoFilled, Download} from '@element-plus/icons-vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import type {UploadFile} from 'element-plus';
import * as api from '@/api/know/graph';
import axios from 'axios';

const env = import.meta.env;
const emits = defineEmits(['getKnowledgeData']);

const showDialog = ref(false);
const fileList = ref<any[]>([]);
const isUploading = ref(false);

// 文件大小限制（10MB）
const MAX_FILE_SIZE = 10 * 1024 * 1024;
// 支持的文件类型
const VALID_FILE_TYPES = ['.csv'];

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  }
};

// 上传前检查
const beforeUpload = (file: File): boolean | Promise<File> => {
  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(MAX_FILE_SIZE)}`);
    return false;
  }

  // 检查文件类型
  const isValidType = VALID_FILE_TYPES.some(type => file.name.toLowerCase().endsWith(type));

  if (!isValidType) {
    ElMessage.error(`文件类型不支持，请上传 ${VALID_FILE_TYPES.join(', ')} 格式的文件`);
    return false;
  }

  return true;
};

// 文件变更处理
const handleFileChange = (uploadFile: UploadFile) => {
  // 更新文件列表
  fileList.value = [uploadFile.raw];
};

// 超出文件数量限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件，请先删除当前文件');
};

// 上传错误处理
const handleError = () => {
  ElMessage.error('文件上传失败，请重试');
};

// 下载模板
const downloadTemplate = async () => {
  try {
    const type = 'csv';
    const res: any = await axios.get(`${env.VITE_BASE_URL}${env.VITE_API_URL}/knowledge/graph/template/download`, {
      params: {type},
      responseType: 'blob',
    });
    const blob = new Blob([res.data], {type: 'application/json'});
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = '知识图谱批量导入模板.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(link.href);
    ElMessage.success('模板下载成功');
  } catch (error) {
    console.error('模板下载失败:', error);
    ElMessage.error('模板下载失败，请重试');
  }
};

// 导入处理
const handleImport = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先上传文件');
    return;
  }

  try {
    isUploading.value = true;

    // 这里应该处理文件上传逻辑
    // 可以使用FormData和axios等工具发送文件到后端
    const formData = new FormData();
    formData.append('file', fileList.value[0]);

    const res = await api.graphImport(formData);

    ElMessage.success(`导入成功`);
    emits('getKnowledgeData');
    setTimeout(() => {
      handleClose();
    }, 1000);
  } catch (error) {
    console.error('导入失败:', error);
    ElMessage.error('导入过程中发生错误，请重试');
  } finally {
    setTimeout(() => {
      isUploading.value = false;
    }, 500);
  }
};

// 关闭弹窗
const handleClose = () => {
  if (isUploading.value) {
    ElMessageBox.confirm('文件正在上传中，确定要取消吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        isUploading.value = false;

        showDialog.value = false;
        fileList.value = [];
      })
      .catch(() => {
        // 用户取消关闭操作
      });
  } else {
    showDialog.value = false;
    fileList.value = [];
  }
};

// 暴露打开方法
const open = () => {
  showDialog.value = true;
  fileList.value = [];
  isUploading.value = false;
};

defineExpose({open});
</script>

<style scoped lang="scss">
.upload-container {
  padding: 20px;
}

.upload-area {
  width: 100%;
  border-radius: 8px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-upload-dragger) {
    padding: 30px 20px;
    border-radius: 8px;
    border: 2px dashed #dcdfe6;

    &:hover {
      border-color: #409eff;
    }
  }

  :deep(.el-icon--upload) {
    font-size: 48px;
    color: #409eff;
    margin-bottom: 16px;
  }
}

.el-upload__text {
  font-size: 16px;
  color: #606266;

  span {
    display: block;
    margin-bottom: 8px;
  }
}

.upload-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #909399;
  margin-top: 12px;

  .el-icon {
    margin-right: 4px;
    color: #e6a23c;
  }
}

.file-info {
  display: flex;
  align-items: center;
  max-width: 70%;
}

.file-icon {
  font-size: 20px;
  color: #409eff;
  margin-right: 10px;
}

.file-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.file-size {
  font-size: 12px;
}

.file-actions {
  display: flex;
  gap: 16px;

  .el-button {
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 4px;
    }
  }
}

.template-download {
  margin-top: 24px;
  text-align: right;

  .el-button {
    display: inline-flex;
    align-items: center;

    .el-icon {
      margin-right: 4px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
