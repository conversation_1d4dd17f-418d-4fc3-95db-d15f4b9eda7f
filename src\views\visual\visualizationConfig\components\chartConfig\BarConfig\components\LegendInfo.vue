<template>
  <div class="form-row">
    <label class="form-label">水平位置</label>
    <el-radio-group
      @change="changeLegendX"
      v-model="legendX"
      size="small"
      class="position-radio-group"
    >
      <el-radio-button label="left">靠左</el-radio-button>
      <el-radio-button label="center">居中</el-radio-button>
      <el-radio-button label="right">靠右</el-radio-button>
    </el-radio-group>
  </div>

  <div class="form-row">
    <label class="form-label">垂直位置</label>
    <el-radio-group
      @change="changeLegendY"
      v-model="legendY"
      size="small"
      class="position-radio-group"
    >
      <el-radio-button label="top">顶部</el-radio-button>
      <el-radio-button label="center">居中</el-radio-button>
      <el-radio-button label="bottom">底部</el-radio-button>
    </el-radio-group>
  </div>

  <div class="form-row">
    <label class="form-label">排列方式</label>
    <el-radio-group class="radio-group" v-model="legendOrient">
      <el-radio value="horizontal">水平排列</el-radio>
      <el-radio value="vertical">垂直排列</el-radio>
    </el-radio-group>
  </div>

  <div class="form-row">
    <label class="form-label">文本样式</label>
    <div class="text-style-toolbar">
      <!-- 字体选择 -->
      <el-select
        v-model="currentFontFamily"
        class="font-select"
        placeholder="选择字体"
        size="small"
        @change="handleFontFamilyChange"
      >
        <el-option label="Arial" value="Arial" />
        <el-option label="Times New Roman" value="Times New Roman" />
        <el-option label="Microsoft YaHei" value="Microsoft YaHei" />
        <el-option label="SimSun" value="SimSun" />
        <el-option label="SimHei" value="SimHei" />
      </el-select>

      <!-- 字号选择 -->
      <el-input-number
        v-model="visualStore.chartConfig[visualStore.chartType].legend.textStyle.fontSize"
        class="font-size-input"
        :min="8"
        :max="72"
        size="small"
        controls-position="right"
      />

      <!-- 样式按钮组 -->
      <div class="style-buttons">
        <!-- 加粗按钮 -->
        <el-button
          :type="isBold ? 'primary' : 'default'"
          size="small"
          class="style-btn bold-btn"
          @click="toggleBold"
        >
          <strong>B</strong>
        </el-button>

        <!-- 斜体按钮 -->
        <el-button
          :type="isItalic ? 'primary' : 'default'"
          size="small"
          class="style-btn italic-btn"
          @click="toggleItalic"
        >
          <em>I</em>
        </el-button>
      </div>

      <!-- 颜色选择器 -->
      <el-color-picker
        v-model="visualStore.chartConfig[visualStore.chartType].legend.textStyle.color"
        size="small"
        class="color-picker"
        show-alpha
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useVisualStore } from "@/store/modules/visual";

const visualStore = useVisualStore();

// 图例水平位置
const legendX = computed({
  get: () => visualStore.chartConfig[visualStore.chartType].legend.x,
  set: (value: string) => {
    visualStore.chartConfig[visualStore.chartType].legend.x = value;
  }
});

// 图例垂直位置
const legendY = computed({
  get: () => {
    // 优先使用 top 属性，如果没有则使用 y 属性
    const legend = visualStore.chartConfig[visualStore.chartType].legend;
    if (legend.top === 'top' || legend.top === '2%' || legend.top === '0%') {
      return 'top';
    } else if (legend.top === 'middle' || legend.top === '50%') {
      return 'center';
    } else if (legend.top === 'bottom' || legend.top === '90%') {
      return 'bottom';
    } else {
      // 兼容旧数据，如果没有 top 属性，则使用 y 属性
      return legend.y || 'top';
    }
  },
  set: (value: string) => {
    const legend = visualStore.chartConfig[visualStore.chartType].legend;
    // 同时设置 y 和 top 属性，确保兼容性
    legend.y = value;

    // 根据选择设置正确的 top 值
    if (value === 'top') {
      legend.top = '2%';
    } else if (value === 'center') {
      legend.top = 'middle';
    } else if (value === 'bottom') {
      legend.top = '90%';
    }
  }
});

// 图例排列方式
const legendOrient = computed({
  get: () => {
    return visualStore.chartConfig[visualStore.chartType].legend.orient || 'horizontal';
  },
  set: (value: string) => {
    visualStore.chartConfig[visualStore.chartType].legend.orient = value;
  }
});

// 当前字体系列的计算属性
const currentFontFamily = computed({
  get: () => {
    const textStyle = visualStore.chartConfig[visualStore.chartType].legend.textStyle;
    return textStyle.fontFamily || 'Arial';
  },
  set: (value: string) => {
    const textStyle = visualStore.chartConfig[visualStore.chartType].legend.textStyle;
    textStyle.fontFamily = value;
  }
});

// 是否加粗的计算属性
const isBold = computed(() => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].legend.textStyle;
  return textStyle.fontWeight === 'bold' || textStyle.fontWeight === 700;
});

// 是否斜体的计算属性
const isItalic = computed(() => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].legend.textStyle;
  return textStyle.fontStyle === 'italic';
});

/**
 * 修改图例的水平位置
 */
const changeLegendX = (position: string) => {
  visualStore.chartConfig[visualStore.chartType].legend.x = position;
};

/**
 * 修改图例的垂直位置
 */
const changeLegendY = (position: string) => {
  const legend = visualStore.chartConfig[visualStore.chartType].legend;

  // 同时设置 y 和 top 属性，确保兼容性
  legend.y = position;

  // 根据选择设置正确的 top 值
  if (position === 'top') {
    legend.top = '2%';
  } else if (position === 'center') {
    legend.top = 'middle';
  } else if (position === 'bottom') {
    legend.top = '90%';
  }

  console.log(`图例垂直位置已更新: y=${position}, top=${legend.top}`);
};

// 处理字体系列变化
const handleFontFamilyChange = (value: string) => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].legend.textStyle;
  textStyle.fontFamily = value;
};

// 切换加粗
const toggleBold = () => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].legend.textStyle;
  if (textStyle.fontWeight === 'bold' || textStyle.fontWeight === 700) {
    textStyle.fontWeight = 'normal';
  } else {
    textStyle.fontWeight = 'bold';
  }
};

// 切换斜体
const toggleItalic = () => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].legend.textStyle;
  if (textStyle.fontStyle === 'italic') {
    textStyle.fontStyle = 'normal';
  } else {
    textStyle.fontStyle = 'italic';
  }
};
</script>

<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18px;

  .form-label {
    width: 80px;
    color: #222;
    font-size: 14px;
    flex-shrink: 0;
    line-height: 32px; // 与输入框高度对齐
  }

  .form-input {
    flex: 1;
    min-width: 0;
  }

  .radio-group {
    margin-left: 8px;

    :deep(.el-radio) {
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.position-radio-group {
  flex: 1;

  :deep(.el-radio-button__inner) {
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  :deep(.el-radio-button.is-active .el-radio-button__inner) {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
  }
}

.text-style-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;

  .font-select {
    width: 140px;
  }

  .font-size-input {
    width: 80px;

    :deep(.el-input__inner) {
      text-align: center;
    }
  }

  .style-buttons {
    display: flex;
    gap: 4px;

    .style-btn {
      width: 32px;
      height: 32px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.bold-btn strong {
        font-size: 14px;
        font-weight: 700;
      }

      &.italic-btn em {
        font-size: 14px;
        font-style: italic;
      }
    }
  }

  .color-picker {
    margin-left: 8px;

    :deep(.el-color-picker__trigger) {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .form-label {
      width: auto;
      line-height: 1.5;
    }
  }

  .position-radio-group {
    width: 100%;
  }

  .text-style-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    width: 100%;

    .font-select {
      width: 100%;
    }

    .style-buttons {
      width: 100%;
      justify-content: flex-start;
    }

    .color-picker {
      margin-left: 0;
    }
  }
}
</style>
