import {createRouter, createWebHashHistory, RouteRecordRaw} from 'vue-router';
import {knowRoutes} from '@/router/modules/know';
import {dataRoutes} from '@/router/modules/data';
import {toolRoutes} from '@/router/modules/tool';
import {qnTestRoutes} from '@/router/modules/qnTest';
import {visualRoutes} from '@/router/modules/visual';
import Layout from '@/Layouts/index.vue';

// 扩展 window 类型解决 TS 报错
declare global {
  interface Window {
    $wujie?: {
      props?: {
        routerType?: string;
      };
    };
  }
}

// 获取主应用传入的 routerType 参数，默认空字符串
const routeType = window.$wujie?.props?.routerType || '';
console.log('routeType:',window.$wujie?.props);

// 合并所有功能模块路由
const allRoutes: RouteRecordRaw[] = [...knowRoutes, ...dataRoutes, ...toolRoutes, ...qnTestRoutes, ...visualRoutes];

// 根据 routerType 决定是否用 Layout 包裹
const routes: RouteRecordRaw[] = [];

if (routeType !== 'page') {
  routes.push({
    path: '/',
    component: Layout,
    redirect: '/know/knowbase',
    children: allRoutes,
  });
} else {
  routes.push(...allRoutes);
}

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;
