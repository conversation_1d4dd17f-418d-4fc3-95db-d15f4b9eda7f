<template>
  <PageHeader title="添加测试问题">
    <template #page-header-back>
      <el-icon class="header-back-icon" @click="cancel()"><ArrowLeftBold /></el-icon>
    </template>
    <template #default>
      <div class="header">
        <el-form :inline="true">
          <el-form-item label="">
            <el-input v-model="testQuestForm.testQuestions" placeholder="输入测试问题" class="search-input" />
          </el-form-item>
          <el-form-item v-show="qnTestStore.paramsForm.type === 'add'">
            <el-button type="primary" @click="test">测试</el-button>
            <el-button type="danger" @click="clearForm">清空</el-button>
          </el-form-item>
          <el-form-item label="是否正确">
            <el-radio-group v-model="testQuestForm.istrue">
              <el-radio label="正确" :value="true">正确</el-radio>
              <el-radio label="错误" :value="false">错误</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="错误原因" v-show="!testQuestForm.istrue">
            <el-select v-model="testQuestForm.errorReason" placeholder="选择错误原因" style="width: 150px">
              <el-option v-for="item in errorOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <div class="result-section">
        <!-- 问题补全 -->
        <div class="result-item">
          <div class="label">问题补全</div>
          <div class="value">{{ testQuestForm.questionCompletion }}</div>
          <div class="time">{{ testQuestForm.completionTime }}s</div>
        </div>

        <!-- 问题拆分 -->
        <div class="result-item">
          <div class="label">问题拆分</div>
          <el-table :data="testQuestForm.askNumberSubs" style="width: 100%">
            <el-table-column type="index" label="子问题编号" width="100" />
            <el-table-column prop="issues" label="拆分后问题" width="250" />
            <el-table-column prop="dataset" label="查找数据集" min-width="180">
              <template #default="scope">
                <div>
                  {{ scope.row.dataset }}
                  <div class="time">{{ scope.row.dataset_time }}s</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="generate" label="生成SQL" min-width="250">
              <template #default="scope">
                <pre>{{ scope.row.generate }}</pre>
                <div class="time">{{ scope.row.generate_time }}s</div>
              </template>
            </el-table-column>
            <el-table-column prop="answer" label="答案" min-width="180" />
          </el-table>
        </div>

        <!-- 答案总结 -->
        <div class="result-item">
          <div class="result-item-label">答案总结</div>
          <div class="result-item-value">{{ testQuestForm.correctAnswer }}</div>
          <div class="result-item-time">{{ testQuestForm.answerTime }}s</div>
        </div>
      </div>

      <div class="content-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="save" v-show="qnTestStore.paramsForm.type !== 'verify'">保存</el-button>
        <el-button type="primary" v-show="qnTestStore.paramsForm.type === 'verify'" @click="next">下一步</el-button>
      </div>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import {ref, onMounted, watch} from 'vue';
import {ElMessage} from 'element-plus';
import {ArrowLeftBold} from '@element-plus/icons-vue';
import * as api from '@/api/qnTest/addQnTest/index';
import {useRouter} from 'vue-router';
import {useQnTestStore} from '@/store/modules/qnTest';
import PageHeader from '@/components/PageHeader.vue';

const router = useRouter();
const qnTestStore = useQnTestStore();

const selectionList = ref(qnTestStore.paramsForm?.selectList);

const errorOptions = ref([
  {value: '补全错误', label: '补全错误'},
  {value: '拆分错误', label: '拆分错误'},
  {value: '查找数据集错误', label: '查找数据集错误'},
  {value: '生成sql错误', label: '生成sql错误'},
  {value: '答案总结错误', label: '答案总结错误'},
]);

const testQuestForm = ref({
  testQuestions: '',
  questionCompletion: '2025年老石旦煤矿产量是多少',
  completionTime: '0.2',
  correctAnswer: '2025年老石旦煤矿产量是5432万吨',
  answerTime: '0.2',
  istrue: true,
  errorReason: '',
  askNumberSubs: [
    {
      issues: '2025年老石旦煤矿产量是多少',
      dataset: '煤矿产量年度汇总表\nads_grid_xnyfzj_qwwxnyfdcl_hf',
      generate: `SELECT data_year AS "年份", voltage_name AS "煤矿名称", data_type AS "商品煤产量", dw AS "单位"
FROM ads_grid_xnyfzj_qwwxnyfdcl_hf
WHERE data_year='2025'`,
      answer: '2025年老石旦煤矿产量是5432万吨',
      dataset_time: '0.2',
      generate_time: '0.2',
    },
  ],
});

onMounted(() => {
  clearForm();
  distinguishTypes();
});

watch(
  () => testQuestForm.value.istrue,
  newVal => {
    if (newVal) {
      testQuestForm.value.errorReason = '';
    }
  },
  {immediate: true, deep: true}
);

const distinguishTypes = () => {
  switch (qnTestStore.paramsForm.type) {
    case 'view':
      getViewData();
      break;
    case 'add':
      break;
    case 'verify':
      verifyInit();
      break;
    default:
      break;
  }
};

const verifyInit = () => {
  batchTestQuery();
  getTestQuestionData();
};

const batchTestQuery = async () => {
  try {
    const idsParam = selectionList.value?.map((item: any) => item.id).join(',');
    await api.askNumberBatchTestQuery({ids: idsParam});
  } catch (error) {
    console.error('批量测试查询失败:', error);
  }
};

const updateTestQuestionData = async () => {
  try {
    await api.askNumberUpdate(testQuestForm.value);
  } catch (error) {
    console.error('更新测试问题失败:', error);
  }
};

const getTestQuestionData = async () => {
  try {
    const res = await api.askNumberBatchTest({id: selectionList?.value?.[0]?.id});
    testQuestForm.value = res;
    selectionList.value?.shift();
  } catch (error) {
    console.error('获取测试问题数据失败:', error);
  }
};

const next = async () => {
  await updateTestQuestionData();
  await getTestQuestionData();
  if (selectionList.value?.length === 0) {
    router.back();
  }
};

const getViewData = async () => {
  try {
    const res = await api.askNumberGet({id: qnTestStore.paramsForm.id});
    testQuestForm.value = res;
  } catch (error) {
    console.error('获取查看数据失败:', error);
  }
};

const test = async () => {
  if (!testQuestForm.value.testQuestions) {
    ElMessage.warning('请输入测试问题');
    return;
  }
  try {
    const res = await api.askNumberTest({question: testQuestForm.value.testQuestions});
    testQuestForm.value = res;
  } catch (error) {
    console.error('测试失败:', error);
    ElMessage.error('测试失败');
  }
};

const cancel = () => {
  router.replace({name: 'QnTestList'});
  qnTestStore.clearQnTest();
};

const save = async () => {
  try {
    await api.askNumberCreate(testQuestForm.value);
    ElMessage.success('保存成功');
    if (qnTestStore.paramsForm.type === 'add') {
      clearForm();
    }
    cancel();
  } catch (error) {
    ElMessage.error('保存失败');
    console.log('保存失败:', error);
  }
};

const clearForm = () => {
  testQuestForm.value = {
    testQuestions: '',
    questionCompletion: '',
    completionTime: '',
    correctAnswer: '',
    answerTime: '',
    istrue: true,
    errorReason: '',
    askNumberSubs: [],
  };
};
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 0px;
}

.result-section {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  flex: 1;
  .result-item {
    margin-bottom: 20px;
    .result-item-label {
      display: inline-block;
      font-weight: bold;
      margin-bottom: 15px;
    }
    .result-item-value {
      display: inline-block;
      width: calc(100% - 150px);
    }

    .result-item-time {
      color: #67c23a;
      float: right;
    }
  }
}
</style>
