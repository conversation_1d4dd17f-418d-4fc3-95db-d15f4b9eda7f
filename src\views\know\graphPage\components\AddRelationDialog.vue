<template>
  <el-dialog v-model="showDialog" title="添加关系" width="30%" :before-close="handleClose">
    <el-form ref="formRef" :model="formData" label-width="80px">
      <el-form-item label="起点实体" prop="startId" :rules="[{required: true, message: '请选择起点实体', trigger: ['blur', 'change']}]">
        <el-autocomplete
          v-model="formData.startId"
          :fetch-suggestions="querySearchStartNode"
          value-key="id"
          placeholder="请选择起点实体"
          :trigger-on-focus="true"
          :popper-append-to-body="true"
          @select="handleSelectStartNode"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #default="{item}">
            {{ item.name }}
          </template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item label="关系" prop="relation" :rules="[{required: true, message: '请输入关系', trigger: ['blur', 'change']}]">
        <el-autocomplete
          value-key="name"
          :trigger-on-focus="true"
          v-model="formData.relation"
          :fetch-suggestions="querySearch"
          placeholder="请输入关系"
          @select="handleSelect"
          :popper-append-to-body="true"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #default="{item}">
            <div :style="{color: item.color}">{{ item.name }}</div>
          </template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item label="反向关系" prop="reverseRelation">
        <span :style="{color: relationTypes.find(r => r.name === formData.relation)?.color || 'inherit'}">
          {{ formData.reverseRelation || '请先选择关系' }}
        </span>
      </el-form-item>
      <el-form-item label="终点实体" prop="endId" :rules="[{required: true, message: '请选择终点实体', trigger: ['blur', 'change']}]">
        <el-autocomplete
          v-model="formData.endId"
          :fetch-suggestions="querySearchEndNode"
          value-key="id"
          placeholder="请选择终点实体"
          :trigger-on-focus="true"
          :popper-append-to-body="true"
          @select="handleSelectEndNode"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #default="{item}">
            {{ item.name }}
          </template>
        </el-autocomplete>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, reactive, defineEmits, nextTick} from 'vue';
import {Search} from '@element-plus/icons-vue';
import {ElForm, ElMessage} from 'element-plus';
import * as api from '@/api/know/graph';

// 定义组件事件
const emits = defineEmits(['confirm', 'getKnowledgeData']);

// 组件状态
const showDialog = ref(false);
const formRef = ref<InstanceType<typeof ElForm> | null>();
const nodeList = ref<any[]>([]);
// 关系类型数据源
let relationTypes = [];

const getNodeList = async () => {
  const nodeData = await api.graphDisplay();
  nodeList.value = nodeData.nodeList;
};

const getRelationTypes = async () => {
  const res = await api.graphRelationshipPage();
  console.log(res, 'res');
  relationTypes = res.list;
};

const querySearchEndNode = (queryString: string, cb: (results: any[]) => void) => {
  const filtered = queryString ? nodeList.value.filter(node => node.name.toLowerCase().includes(queryString.toLowerCase())) : [...nodeList.value];

  cb(filtered);
};

const querySearchStartNode = (queryString: string, cb: (results: any[]) => void) => {
  const filtered = queryString ? nodeList.value.filter(node => node.name.toLowerCase().includes(queryString.toLowerCase())) : [...nodeList.value];

  cb(filtered);
};

const querySearch = (queryString: string, cb: (results: {value: string; name: string; inverseName: string; color: string}[]) => void) => {
  const filtered = queryString
    ? relationTypes.filter(rel => rel.name.toLowerCase().includes(queryString.toLowerCase()) || rel.inverseName.toLowerCase().includes(queryString.toLowerCase()))
    : [...relationTypes];

  // 将所需的 'value' 属性添加到每个结果
  const results = filtered.map(rel => ({
    ...rel,
    value: rel.name, // 使用 'type' 作为输入的值
  }));

  cb(results);
};

const handleSelectStartNode = (item: any) => {
  formData.startId = item.name;
};

const handleSelectEndNode = (item: any) => {
  formData.endId = item.name;
};

const handleSelect = (item: {name: string; inverseName: string; color: string}) => {
  formData.relation = item.name;
  formData.reverseRelation = item.inverseName;
};

const formData = reactive({
  startId: '',
  relation: '',
  reverseRelation: '',
  endId: '',
});

// 打开弹窗方法
const open = async (nodeName?: string) => {
  formRef.value?.clearValidate();
  await getNodeList();
  await getRelationTypes();
  showDialog.value = true;
  // 重置表单数据
  formData.startId = nodeName || '';
  formData.relation = '';
  formData.reverseRelation = '';
  formData.endId = '';

  // 等待DOM更新后清除校验状态
  await nextTick();
};

// 关闭弹窗方法
const handleClose = () => {
  showDialog.value = false;
};

// 确认添加关系
const handleConfirm = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    // emits('confirm', {...formData});
    const startId = nodeList.value.find(item => item.name === formData.startId)?.id;
    const endId = nodeList.value.find(item => item.name === formData.endId)?.id;
    const relationShipTypeId = relationTypes.find(item => item.name === formData.relation)?.id;
    const params = {
      startId,
      endId,
      relationShipTypeId,
    };
    await api.graphCreateRelationship(params);
    ElMessage.success('添加关系成功');
    emits('getKnowledgeData');
    showDialog.value = false;
  } catch (error) {
    console.log('表单验证失败:', error);
  }
};

// 暴露组件方法
defineExpose({open});
</script>

<style scoped>
.dialog-footer {
  z-index: 10;
}

:deep(.el-autocomplete) {
  z-index: 1000;
}

:deep(.el-popper) {
  z-index: 1001 !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
