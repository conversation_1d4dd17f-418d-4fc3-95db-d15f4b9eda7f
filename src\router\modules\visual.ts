import type {RouteRecordRaw} from 'vue-router';
import DatasetCreate from '@/views/dataAnalysis/dataSet/datasetCreate.vue';
export const visualRoutes: RouteRecordRaw[] = [
  {
    path: '/visual',
    meta: {title: '可视化管理', grouping: true, menu: true, icon: 'menu/visualizationManage'},
    children: [
      {
        path: 'visualization',
        name: 'Visualization',
        component: () => import('@/views/visual/visualization/index.vue'),
        meta: {title: '可视化', menu: true, icon: 'menu/visualization'},
      },
      {
        path: 'visualizationconfig',
        name: 'VisualizationConfig',
        component: () => import('@/views/visual/visualizationConfig/index.vue'),
        meta: {title: '可视化', menu: false, isActive: '/visual/visualization'},
      },
      {
        path: 'visualization-config',
        name: 'VisualizationConfigNew',
        component: () => import('@/views/visual/visualizationConfig/index.vue'),
        meta: {title: '可视化配置', menu: false, isActive: '/visual/visualization'},
      },
    ],
  },
];
