<template>
  <div class="card-config-container">
    <!-- 卡片背景配置 - 只影响当前选中的图表 -->
    <div class="config-section">
      <div class="form-row">
        <label class="form-label">卡片背景</label>
        <div class="background-controls">
          <SelectColor
            ref="selectColorRef"
            :initialColor="currentBackgroundColor"
            :initialMode="currentColorMode"
            @update:color="handleColorUpdate"
            @modeChange="handleModeChange"
            :key="`color-${visualStore.chartType}-${currentBackgroundColor}-${currentColorMode}`"
          />
        </div>
      </div>
    </div>

    <!-- 背景图片配置 -->
    <div class="config-section">
      <div class="form-row">
        <label class="form-label">背景图片</label>
        <div class="image-controls">
          <el-popover
            placement="top"
            :width="400"
            trigger="click"
            title="背景图片设置"
            popper-class="image-upload-popover"
          >
            <template #default>
              <div class="upload-container">
                <!-- 本地上传区域 -->
                <div class="upload-section">
                  <h4 class="section-title">本地上传</h4>
                  <el-upload
                    class="upload-demo"
                    drag
                    action="#"
                    :show-file-list="false"
                    :limit="1"
                    :auto-upload="false"
                    :file-list="fileList"
                    @change="handleFileChange"
                    accept=".jpg,.jpeg,.png,.gif,.svg,.webp"
                  >
                    <el-icon class="el-icon--upload"><Upload /></el-icon>
                    <div class="el-upload__text">
                      将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <div class="upload-tip">
                      支持 jpg, jpeg, png, gif, svg, webp 格式，最大 2M
                    </div>
                  </el-upload>
                </div>

                <!-- 网络图片区域 -->
                <div class="url-section">
                  <h4 class="section-title">网络图片</h4>
                  <div class="upload-link-row">
                    <el-input
                      v-model="customImgUrl"
                      placeholder="请输入图片网址"
                      class="upload-link-input"
                      clearable
                    />
                    <el-button
                      type="primary"
                      @click="useCustomImg"
                      :disabled="!customImgUrl"
                      :loading="imageLoading"
                    >
                      使用
                    </el-button>
                  </div>
                </div>

                <!-- 图片预览和操作 -->
                <div class="image-preview-section" v-if="currentBackgroundImage">
                  <h4 class="section-title">当前图片</h4>
                  <div class="image-preview">
                    <img :src="currentBackgroundImage" alt="背景图片预览" />
                    <div class="image-actions">
                      <el-button size="small" @click="clearBackgroundImage" type="danger" plain>
                        清空图片
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <template #reference>
              <div class="image-upload-trigger">
                <div
                  class="image-upload-placeholder"
                  v-if="!currentBackgroundImage"
                >
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <span class="upload-text">添加背景图片</span>
                </div>
                <div class="image-preview-thumb" v-else>
                  <img :src="currentBackgroundImage" alt="背景图片" />
                  <div class="image-overlay">
                    <el-icon><Edit /></el-icon>
                  </div>
                </div>
              </div>
            </template>
          </el-popover>
        </div>
      </div>
    </div>

    <!-- 卡片边框配置 -->
    <div class="config-section">
      <div class="form-row">
        <label class="form-label">外框样式</label>
        <div class="border-controls">
          <div class="border-color-group">
            <label class="control-label">颜色</label>
            <el-color-picker
              v-model="borderColor"
              size="small"
              show-alpha
              @change="handleBorderColorChange"
            />
          </div>

          <div class="border-width-group">
            <label class="control-label">宽度</label>
            <el-input-number
              v-model="borderWidth"
              :min="0"
              :max="20"
              size="small"
              controls-position="right"
              @change="handleBorderWidthChange"
            />
            <span class="unit">px</span>
          </div>

          <div class="border-style-group">
            <label class="control-label">样式</label>
            <el-select
              v-model="borderStyle"
              size="small"
              @change="handleBorderStyleChange"
              placeholder="选择边框样式"
            >
              <el-option label="实线" value="solid" />
              <el-option label="虚线" value="dashed" />
              <el-option label="点线" value="dotted" />
              <el-option label="双线" value="double" />
            </el-select>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片圆角配置 -->
    <div class="config-section">
      <div class="form-row">
        <label class="form-label">外框圆角</label>
        <div class="radius-controls">
          <el-input-number
            v-model="borderRadius"
            :min="0"
            :max="50"
            size="small"
            controls-position="right"
            @change="handleBorderRadiusChange"
          />
          <span class="unit">px</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { useVisualStore } from "@/store/modules/visual";
import SelectColor from '@/components/SelectColor/SelectColor.vue';
import { Upload, Plus, Edit } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { uploadImage, getImageUrl } from '@/api/visualTheme';

// 类型定义
interface UploadFile {
  raw?: File;
  name: string;
  size: number;
}

const selectColorRef = ref(null);
const visualStore = useVisualStore();
const customImgUrl = ref('');
const fileList = ref<UploadFile[]>([]);
const imageLoading = ref(false);

// 确保 cardStyle 对象存在的函数
const ensureCardStyle = () => {
  const config = visualStore.chartConfig[visualStore.chartType];
  if (!config.cardStyle) {
    config.cardStyle = {
      borderColor: "#000000",
      borderWidth: 1,
      borderStyle: "solid",
      borderRadius: 0,
    };
  }
};

// 组件挂载时确保数据结构正确
onMounted(() => {
  ensureCardStyle();
});

// 计算属性 - 响应式地获取当前图表的配置
const currentColorMode = computed(() => {
  const mode = visualStore.chartConfig[visualStore.chartType].colorMode || 'solid';
  console.log(`Current chart: ${visualStore.chartType}, colorMode: ${mode}`);
  return mode;
});

const currentBackgroundColor = computed(() => {
  const color = visualStore.chartConfig[visualStore.chartType].backgroundColor || '#ffffff';
  console.log(`Current chart: ${visualStore.chartType}, backgroundColor: ${color}`);
  return color;
});

const currentBackgroundImage = computed(() => {
  return visualStore.chartConfig[visualStore.chartType].graphic?.style?.image || '';
});

const borderColor = computed({
  get: () => {
    ensureCardStyle();
    return visualStore.chartConfig[visualStore.chartType].cardStyle.borderColor;
  },
  set: (value: string) => {
    ensureCardStyle();
    visualStore.chartConfig[visualStore.chartType].cardStyle.borderColor = value;
  }
});

const borderWidth = computed({
  get: () => {
    ensureCardStyle();
    return visualStore.chartConfig[visualStore.chartType].cardStyle.borderWidth;
  },
  set: (value: number) => {
    ensureCardStyle();
    visualStore.chartConfig[visualStore.chartType].cardStyle.borderWidth = value;
  }
});

const borderStyle = computed({
  get: () => {
    ensureCardStyle();
    return visualStore.chartConfig[visualStore.chartType].cardStyle.borderStyle;
  },
  set: (value: string) => {
    ensureCardStyle();
    visualStore.chartConfig[visualStore.chartType].cardStyle.borderStyle = value;
  }
});

const borderRadius = computed({
  get: () => {
    ensureCardStyle();
    return visualStore.chartConfig[visualStore.chartType].cardStyle.borderRadius;
  },
  set: (value: number) => {
    ensureCardStyle();
    visualStore.chartConfig[visualStore.chartType].cardStyle.borderRadius = value;
  }
});

/**
 * 处理本地图片上传
 */
async function handleFileChange(file: UploadFile) {
  const raw = file.raw as File;

  if (!raw) {
    ElMessage.error('文件上传失败');
    return;
  }

  // 文件类型和大小判断
  const isImage = /\.(jpg|jpeg|png|gif|svg|webp)$/i.test(raw.name);
  const isLt2M = raw.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只支持 jpg、jpeg、png、gif、svg、webp 格式');
    return;
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2M');
    return;
  }

  try {
    // 调用上传接口
    const response = await uploadImage(raw);

    console.log('图表背景图片上传响应:', response);

    if (response.code === 200) {
      // 使用文件名生成访问地址
      const imageUrl = getImageUrl(response.data.fileName);
      console.log('生成的图表背景图片URL:', imageUrl);

      // 确保 graphic 对象存在
      if (!visualStore.chartConfig[visualStore.chartType].graphic) {
        visualStore.chartConfig[visualStore.chartType].graphic = {
          type: "image",
          id: "background",
          style: {
            image: "",
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
          },
          z: -1,
        };
      }

      visualStore.chartConfig[visualStore.chartType].graphic.style.image = imageUrl;
      ElMessage.success('图片上传成功');
    } else {
      ElMessage.error(response.message || '上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error('上传失败，请重试');
  }

  // 清空 fileList 防止重复不触发
  fileList.value = [];
}

/**
 * 使用外链图片
 */
async function useCustomImg() {
  if (!customImgUrl.value) {
    ElMessage.warning('请输入图片网址');
    return;
  }

  imageLoading.value = true;

  try {
    // 验证图片是否可以加载
    await validateImageUrl(customImgUrl.value);
    visualStore.chartConfig[visualStore.chartType].graphic.style.image = customImgUrl.value;
    ElMessage.success('图片设置成功');
    customImgUrl.value = '';
  } catch (error) {
    ElMessage.error('图片加载失败，请检查网址是否正确');
  } finally {
    imageLoading.value = false;
  }
}

/**
 * 验证图片URL是否有效
 */
function validateImageUrl(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = () => reject();
    img.src = url;
  });
}

/**
 * 清空背景图片
 */
function clearBackgroundImage() {
  visualStore.chartConfig[visualStore.chartType].graphic.style.image = '';
  ElMessage.success('背景图片已清空');
}

// 处理颜色更新事件
function handleColorUpdate(color: string) {
  visualStore.chartConfig[visualStore.chartType].backgroundColor = color;
}

// 处理模式变化事件
function handleModeChange(mode: string) {
  visualStore.chartConfig[visualStore.chartType].colorMode = mode;
}



// 边框相关处理函数 - 只修改当前选中图表的卡片样式
function handleBorderColorChange(color: string) {
  borderColor.value = color;
}

function handleBorderWidthChange(width: number) {
  borderWidth.value = width;
}

function handleBorderStyleChange(style: string) {
  borderStyle.value = style;
}

function handleBorderRadiusChange(radius: number) {
  borderRadius.value = radius;
}
</script>

<style scoped lang="scss">
.card-config-container {
  .config-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;

  .form-label {
    width: 80px;
    color: #222;
    font-size: 14px;
    flex-shrink: 0;
    line-height: 32px;
    font-weight: 500;
  }
}

// 背景控制区域
.background-controls {
  flex: 1;
}

// 图片控制区域
.image-controls {
  flex: 1;
}

.image-upload-trigger {
  .image-upload-placeholder {
    width: 120px;
    height: 80px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .upload-icon {
      font-size: 24px;
      color: #8c939d;
      margin-bottom: 4px;
    }

    .upload-text {
      font-size: 12px;
      color: #8c939d;
    }
  }

  .image-preview-thumb {
    width: 120px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .el-icon {
        color: white;
        font-size: 20px;
      }
    }

    &:hover .image-overlay {
      opacity: 1;
    }
  }
}

// 边框控制区域
.border-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  flex-wrap: wrap;

  .border-color-group,
  .border-width-group,
  .border-style-group {
    display: flex;
    align-items: center;
    gap: 8px;
    :deep(.el-select) {
      width: 100px;
    }
  }

  .control-label {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
  }

  .unit {
    font-size: 12px;
    color: #999;
    margin-left: 4px;
  }
}

// 圆角控制区域
.radius-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;

  .unit {
    font-size: 12px;
    color: #999;
  }
}

// 上传容器样式
.upload-container {
  .upload-section,
  .url-section,
  .image-preview-section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
    margin-top: 0;
  }

  .upload-demo {
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 120px;
      border-radius: 8px;
      border: 2px dashed #d9d9d9;
      background: #fafafa;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }
    }

    :deep(.el-icon--upload) {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 8px;
    }

    :deep(.el-upload__text) {
      color: #606266;
      font-size: 14px;

      em {
        color: #409eff;
        font-style: normal;
      }
    }
  }

  .upload-tip {
    text-align: center;
    color: #999;
    font-size: 12px;
    margin-top: 8px;
  }

  .upload-link-row {
    display: flex;
    align-items: center;
    gap: 8px;

    .upload-link-input {
      flex: 1;
    }
  }

  .image-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    img {
      max-width: 200px;
      max-height: 120px;
      border-radius: 8px;
      object-fit: cover;
      border: 1px solid #e4e7ed;
    }

    .image-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .form-label {
      width: auto;
      line-height: 1.5;
    }
  }

  .border-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .border-color-group,
    .border-width-group,
    .border-style-group {
      width: 100%;
      justify-content: space-between;
    }
  }

  .image-upload-trigger {
    .image-upload-placeholder,
    .image-preview-thumb {
      width: 100%;
      max-width: 200px;
    }
  }
}

// Popover 自定义样式
:global(.image-upload-popover) {
  .el-popover__title {
    font-weight: 500;
    color: #333;
  }
}
</style>