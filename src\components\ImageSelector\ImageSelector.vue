<template>
  <el-popover placement="top" :width="365" trigger="click" :title="label">
    <template #default>
      <el-tabs v-model="tab">
        <el-tab-pane label="使用素材" name="material">
          <div class="image-body">
            <span>{{ label }}</span>
            <div class="image-list">
              <img
                v-for="item in imgList"
                :key="item.url"
                class="image-list-item"
                :class="{'selected-img-border': modelValue === item.url}"
                :src="item.url"
                alt=""
                @click="updateValue(item.url)"
              />
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="自定义图片" name="customize">
          <div class="image-body">
            <el-upload
              class="upload-demo"
              drag
              action="#"
              :show-file-list="false"
              :limit="1"
              :auto-upload="false"
              :file-list="fileList"
              @change="handleFileChange"
              accept=".jpg,.jpeg,.png,.gif,.svg"
            >
              <el-button type="primary" plain :icon="Upload">上传本地图片</el-button>
              <div class="upload-tip">只支持 jpg, jpeg, png, gif, svg 格式，最大 1M</div>
            </el-upload>
            <div class="upload-link-row">
              <el-input v-model="customImgUrl" placeholder="请输入图片网址" class="upload-link-input" />
              <el-button type="primary" @click="useCustomImg" :disabled="!customImgUrl">使用</el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="image-popover-footer">
        <el-button type="primary" link @click="updateValue('')">清空图片</el-button>
      </div>
    </template>
    <template #reference>
      <div class="image-upload-placeholder" v-if="!modelValue">
        <span class="plus">+</span>
      </div>
      <img v-else class="image-upload-placeholder-img" :src="modelValue" alt="" />
    </template>
  </el-popover>
</template>

<script setup>
import {ref} from 'vue';
import {Upload} from '@element-plus/icons-vue';
import {ElMessage} from 'element-plus';
import { uploadImage, getImageUrl, testImageUrl } from '@/api/visualTheme';

const props = defineProps({
  modelValue: String,
  label: { type: String, default: '图片' },
  imgList: { type: Array, default: () => [] }
});
const emit = defineEmits(['update:modelValue']);

const tab = ref('material');
const customImgUrl = ref('');
const fileList = ref([]);

function updateValue(val) {
  emit('update:modelValue', val);
}

async function handleFileChange(file) {
  const raw = file.raw || file;
  const isImage = /\.(jpg|jpeg|png|gif|svg)$/i.test(raw.name);
  const isLt1M = raw.size / 1024 / 1024 < 1;
  if (!isImage) {
    ElMessage.error('只支持 jpg、jpeg、png、gif、svg 格式');
    return;
  }
  if (!isLt1M) {
    ElMessage.error('图片大小不能超过 1M');
    return;
  }

  try {
    // 调用上传接口
    const response = await uploadImage(raw);

    console.log('图片上传响应:', response);

    if (response.code === 200 && response.data && response.data.fileName) {
      // 使用文件名生成访问地址
      const imageUrl = getImageUrl(response.data.fileName);
      console.log('生成的图片URL:', imageUrl);

      // 测试图片URL是否可以访问
      const isAccessible = await testImageUrl(imageUrl);
      if (isAccessible) {
        updateValue(imageUrl);
        ElMessage.success('图片上传成功');
      } else {
        // 如果图片URL不可访问，尝试使用不同的URL格式
        const alternativeUrl = `https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/upload?fileName=${response.data.fileName}`;
        console.log('尝试替代URL:', alternativeUrl);

        const isAlternativeAccessible = await testImageUrl(alternativeUrl);
        if (isAlternativeAccessible) {
          updateValue(alternativeUrl);
          ElMessage.success('图片上传成功(使用替代URL)');
        } else {
          ElMessage.warning('图片已上传，但无法预览，请刷新页面');
          updateValue(imageUrl); // 仍然使用原始URL
        }
      }
    } else {
      ElMessage.error(response.message || '上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error('上传失败，请重试');
  }

  fileList.value = [];
}

function useCustomImg() {
  updateValue(customImgUrl.value);
  ElMessage.success('已使用图片链接');
}
</script>

<style scoped lang="scss">
.image-upload-placeholder,
.image-upload-placeholder-img {
  width: 100px;
  height: 70px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ddd;
  .plus {
    font-size: 32px;
    color: #666;
    font-weight: bold;
  }
}
.image-upload-placeholder-img {
  object-fit: cover;
  background: none;
}
.image-body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 135px;
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
    .image-list-item {
      width: 109px;
      height: 50px;
      border: 3px solid transparent;
      padding: 2px;
      box-sizing: border-box;
      border-radius: 4px;
      transition: border-color 0.2s;
      &.selected-img-border {
        border-color: #409eff;
      }
    }
  }
}
.image-popover-footer {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ccc;
  text-align: right;
}
.upload-demo {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  :deep(.el-upload-dragger) {
    border: none;
    padding: 0;
    text-align: left;
  }
}
.upload-tip {
  text-align: left;
  color: #888;
  font-size: 13px;
  margin-top: 15px;
}
.upload-link-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  .upload-link-input {
    flex: 1;
  }
}
</style> 