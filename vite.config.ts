import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  optimizeDeps: {
    include: [
      '@antv/g6',
    ]
  },
  build: {
    rollupOptions: {
      external: [
        // 确保 @antv/g6 不在此列表中
      ],
      output: {
        globals: {
        }
      }
    },
    commonjsOptions: {
      include: [
        /node_modules/,
        // 确保包含 @antv/g6
        /@antv[\\/]g6/,
      ]
    },
    // 增加构建配置来处理 ES 模块
    target: 'esnext',
    minify: 'terser'
  },
  // 添加 SSR 配置来处理模块解析
  ssr: {
    noExternal: ['@antv/g6']
  }
})