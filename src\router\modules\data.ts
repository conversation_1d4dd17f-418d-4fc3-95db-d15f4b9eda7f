import type {RouteRecordRaw} from 'vue-router';
import DatasetCreate from '@/views/dataAnalysis/dataSet/datasetCreate.vue';
export const dataRoutes: RouteRecordRaw[] = [
  {
    path: '/data',
    meta: {title: '数据管理', grouping: true, menu: true, icon: 'menu/dataManage'},
    children: [
      {
        path: 'datasource',
        name: 'DataSource',
        component: () => import('@/views/dataAnalysis/dataSource/index.vue'),
        meta: {title: '数据源', menu: true, icon: 'menu/dataSource'},
      },
      {
        path: 'dataset',
        name: 'DataSet',
        component: () => import('@/views/dataAnalysis/dataSet/index.vue'),
        meta: {title: '数据集', menu: true, icon: 'menu/dataSet'},
      },
      {
        path: 'newdataset',
        name: 'NewDataSet',
        component: DatasetCreate,
        meta: {
          title: '新建数据集',
          menu: false,
          isActive: '/data/dataset',
        },
      },
    ],
  },
];
