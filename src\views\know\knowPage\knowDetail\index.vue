<template>
  <PageHeader :title="`知识库-${knowStore.knowParamsForm.name}`">
    <template #page-header-back>
      <el-icon class="header-back-icon" @click="goBack()"><ArrowLeftBold /></el-icon>
    </template>
    <template #default>
      <div class="content-header">
        <el-input v-model="searchText" placeholder="搜索" class="search-input" :prefix-icon="Search" @keyup.enter="getTableData" />
        <div>
          <el-button type="danger" plain @click="batchDelete">批量删除</el-button>
          <el-button type="primary" plain @click="startTest">命中测试</el-button>
          <el-button type="primary" @click="triggerUpload">上传文件</el-button>
        </div>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <el-table ref="fileTable" :data="documentFiles" style="width: 100%" max-height="630px" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="文件名称" min-width="180" />
        <el-table-column prop="fileSize" label="数据量" width="120" />
        <el-table-column prop="fileFormat" label="文件格式" width="120" />
        <el-table-column prop="status" label="状态" width="100" />
        <el-table-column prop="creatorName" label="上传人" width="120" />
        <el-table-column prop="createTime" label="上传时间" width="200" />
        <el-table-column label="操作" width="150" align="center">
          <template #default="scope">
            <div class="operation">
              <el-button link type="primary" @click="viewFile(scope.row.id)">查看切片</el-button>
              <el-button link type="danger" @click="deleteFile(scope.row.id)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="content-footer">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
      <!-- 隐藏的上传组件 -->
      <!-- <el-upload
        ref="uploadRef"
        :action="`${env.VITE_BASE_URL}${env.VITE_API_URL}/documents/upload`"
        :headers="uploadHeaders"
        :data="uploadData"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :show-file-list="false"
        accept=".doc,.docx,.ppt,.pptx"
        style="display: none"
      /> -->
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import {ref, reactive, computed, onMounted} from 'vue';
import {Search, ArrowLeftBold} from '@element-plus/icons-vue';
import {useRouter} from 'vue-router';
import {ElMessage, ElMessageBox} from 'element-plus';
import * as api from '@/api/know/knowPage/knowDetail/index';
import {getAccessToken, getTenantId} from '@/utils/auth';
import {useKnowStore} from '@/store/modules/know';
import PageHeader from '@/components/PageHeader.vue';

const knowStore = useKnowStore();

// const env = import.meta.env;
const router = useRouter();
const documentFiles = ref<any[]>([]);
const searchText = ref('');
const multipleSelection = ref<any[]>([]);
// const uploadRef = ref<any | null>(null);
const knowledgeBaseId = ref(knowStore.knowParamsForm.id);

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: computed(() => documentFiles.value.length || 0),
});

// 新增分页重置方法
const resetPagination = () => {
  pagination.currentPage = 1;
  pagination.pageSize = 10;
};

// 获取表格数据（添加重置调用）
const getTableData = async () => {
  resetPagination();
  try {
    const res = await api.documentsGet({
      knowledgeId: knowledgeBaseId.value,
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
    });
    documentFiles.value = res.list || [];
  } catch (error) {
    console.log('error', error);
  }
};

// 分页大小变更
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  getTableData();
};

// 当前页变更
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getTableData();
};

// 上传数据
// const uploadData = computed(() => ({
//   knowledgeBaseId: knowledgeBaseId.value || '',
// }));

// 上传头部信息
// const uploadHeaders = computed(() => ({
//   'tenant-id': getTenantId() || '',
//   Authorization: getAccessToken() || '',
// }));

// 处理选择变更
const handleSelectionChange = (val: any[]) => {
  multipleSelection.value = val.map(item => ({
    id: item.id,
  }));
};

// 批量删除
const batchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请先选择要删除的文件');
    return;
  }

  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 个文件吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const idsParam = multipleSelection.value.map(item => item.id);
        await api.documentsDelete(idsParam);
        getTableData();
        ElMessage.success('批量删除成功');
      } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error('批量删除失败');
      }
    })
    .catch(() => {});
};

// 开始测试
const startTest = () => {
  router.push({
    name: 'HitTesting',
  });
};

// 触发上传
const triggerUpload = () => {
  // uploadRef.value?.$el?.querySelector('input')?.click();
  // router.push({name: 'CreateKnowBase'});
  const route = router.resolve({name: 'CreateKnowBase'});
  window.open(route.href, '_blank');
  knowStore.setKnowParamsForm({
    ...knowStore.knowParamsForm,
    type: 'addFeedback',
  });
};

// 上传前验证
// const beforeUpload = (file: File) => {
//   const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
//   const maxSizeMB = 50;

//   if (!allowedTypes.includes(file.type)) {
//     ElMessage.error('仅支持 PDF 或 Word 文档');
//     return false;
//   }

//   if (file.size / 1024 / 1024 > maxSizeMB) {
//     ElMessage.error('文件大小不能超过 50MB');
//     return false;
//   }

//   return true;
// };

// 处理上传成功
// const handleUploadSuccess = () => {
//   ElMessage.success('上传成功');
//   getTableData();
// };

// 处理上传失败
// const handleUploadError = () => {
//   ElMessage.error('上传失败');
// };

// 查看文件
const viewFile = (documentId: string | number) => {
  router.push({
    name: 'sliceDetails',
  });
  knowStore.setDocumentId(documentId);
};

// 删除文件
const deleteFile = (id: string | number) => {
  ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await api.documentsDelete([id]);
        ElMessage.success('删除成功');
        getTableData();
      } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {});
};

const goBack = () => {
  router.push({name: 'knowBase'});
};

onMounted(() => {
  knowStore.clearDocumentId();
  getTableData();
});
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  height: 100%;
}
.action-bar {
  display: flex;
  justify-content: space-between;
  .search-input {
    width: 300px;
  }
}

.operation {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}
</style>
