import {defineConfig, loadEnv} from 'vite';
import vue from '@vitejs/plugin-vue';
import * as path from 'path'; // 新增path模块导入
// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd();
// 路径查找
function pathResolve(dir) {
  return path.resolve(root, '.', dir);
}
export default defineConfig(({command, mode}) => {
  const env = loadEnv(mode, root);
  return {
    base: env.VITE_BASE_PATH,
    define: {
      __APP_ENV__: JSON.stringify(env.APP_ENV),
    },
    plugins: [vue()],
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'],
      alias: {
        '@': pathResolve('src'),
        'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
      },
    },
    server: {
      headers: {
        'Access-Control-Allow-Origin': '*', // 开发环境
      },
      port: env.VITE_PORT, // 端口号
      host: '0.0.0.0',
      open: env.VITE_OPEN === 'true',
      // 本地跨域代理. 目前注释的原因：暂时没有用途，server 端已经支持跨域
      // proxy: {
      //   ['/admin-api']: {
      //     target: env.VITE_BASE_URL,
      //     ws: false,
      //     changeOrigin: true,
      //     rewrite: (path) => path.replace(new RegExp(`^/admin-api`), ''),
      //   },
      // },
    },
    build: {
      // 新增公共资源目录配置
      assetsDir: 'static',
      outDir: env.VITE_OUT_DIR || 'dist',
      sourcemap: env.VITE_SOURCEMAP === 'true' ? 'inline' : false,
      terserOptions: {
        compress: {
          drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
          drop_console: env.VITE_DROP_CONSOLE === 'true',
        },
      },
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            vendor: ['vue', 'element-plus', 'pinia'],
          },
        },
      },
      chunkSizeWarningLimit: 1000, // 调整 chunk 大小警告阈值
      cssCodeSplit: true, // 启用 CSS 代码分割
    },
  };
});
//# sourceMappingURL=vite.config.js.map
