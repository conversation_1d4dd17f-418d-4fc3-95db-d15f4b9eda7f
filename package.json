{"name": "wuhai-knowledge-management-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build:dev": "vite build --mode dev", "build:prod": "vite build --mode prod", "preview": "vite preview"}, "dependencies": {"@antv/g6": "^5.0.49", "@antv/x6": "^2.18.1", "axios": "^1.6.2", "dagre": "^0.8.5", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "2.9.1", "jsencrypt": "^3.3.2", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.14.0", "vue": "^3.5.16", "vue-router": "^4.2.4", "vue3-colorpicker": "^2.3.0", "web-storage-cache": "^1.1.1", "wujie-vue3": "^1.0.28"}, "devDependencies": {"@types/dagre": "^0.7.52", "@types/node": "^22.15.29", "@types/qs": "^6.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.89.1", "typescript": "5.3.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}