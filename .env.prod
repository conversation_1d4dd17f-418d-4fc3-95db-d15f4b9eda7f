# 开发环境：本地只启动前端项目，依赖开发环境（后端、APP）
NODE_ENV=production

VITE_DEV=true

# 项目本地运行端口号
VITE_PORT=7781

# open 运行 npm run dev 时自动打开浏览器
VITE_OPEN=true

# 租户开关
VITE_APP_TENANT_ENABLE=true

# 请求路径
# VITE_BASE_URL='http://api-dashboard.yudao.iocoder.cn'
# VITE_BASE_URL='http://10.30.224.209:48080'
# VITE_BASE_URL='https://ms72uj6tveex.ngrok.xiaomiqiu123.top'
VITE_BASE_URL='http://8.145.32.133'
# VITE_BASE_URL='http://10.30.127.7:8086'

# 接口地址
# VITE_API_URL=/admin-api
VITE_API_URL=/basic

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/basic_web/

# 输出路径
VITE_OUT_DIR=basic_web
