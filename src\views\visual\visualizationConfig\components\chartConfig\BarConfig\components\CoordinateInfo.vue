<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane
      :label="item.label"
      :name="item.name"
      v-for="item in axisOptions"
      :key="item.name"
    >
      <div>
        <el-checkbox
          v-model="
            visualStore.chartConfig[visualStore.chartType][item.name].axisLine
              .show
          "
          label="显示坐标轴"
          size="large"
        />
        <div class="form-row">
          <label class="form-label"></label>
          <el-select
            v-model="
              visualStore.chartConfig[visualStore.chartType][item.name].axisLine
                .lineStyle.type
            "
            class="m-2"
            size="small"
            style="width: 100px"
          >
            <el-option value="solid" label="实线">实线</el-option>
            <el-option value="dashed" label="虚线">虚线</el-option>
            <el-option value="dotted" label="点线">点线</el-option>
          </el-select>
          <el-input-number
            v-model="
              visualStore.chartConfig[visualStore.chartType][item.name].axisLine
                .lineStyle.width
            "
            class="mx-4"
            :min="1"
            :max="10"
            size="small"
            controls-position="right"
          />
          <el-color-picker
            v-model="
              visualStore.chartConfig[visualStore.chartType][item.name].axisLine
                .lineStyle.color
            "
          />
        </div>
      </div>

      <div>
        <el-checkbox
          v-model="
            visualStore.chartConfig[visualStore.chartType][item.name].splitLine
              .show
          "
          label="显示网格线"
          size="large"
        />
        <div class="form-row">
          <label class="form-label"></label>
          <el-select
            v-model="
              visualStore.chartConfig[visualStore.chartType][item.name].splitLine
                .lineStyle.type
            "
            class="m-2"
            size="small"
            style="width: 100px"
          >
            <el-option value="solid" label="实线">实线</el-option>
            <el-option value="dashed" label="虚线">虚线</el-option>
            <el-option value="dotted" label="点线">点线</el-option>
          </el-select>
          <el-input-number
            v-model="
              visualStore.chartConfig[visualStore.chartType][item.name]
                .splitLine.lineStyle.width
            "
            class="mx-4"
            :min="1"
            :max="10"
            size="small"
            controls-position="right"
          />
          <el-color-picker
            v-model="
              visualStore.chartConfig[visualStore.chartType][item.name]
                .splitLine.lineStyle.color
            "
          />
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
  <!-- <div class="form-row">
    <label class="form-label">标题</label>
    <el-input class="form-input" placeholder="请输入标题" v-model="visualStore.chartConfig[visualStore.chartType].title.text" />
  </div> -->
</template>

<script lang="ts" setup>
import { ref, toRaw, watch } from "vue";
import { useVisualStore } from "@/store/modules/visual";
const activeName = ref("xAxis");
const handleClick = (tab: any, event: Event) => {
  console.log(tab, event);
};
const visualStore = useVisualStore();
const axisOptions = ref([
  {
    label: "X轴",
    name: "xAxis",
  },
  {
    label: "左Y轴",
    name: "yAxis",
  },
]);
</script>

<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;

  .form-label {
    width: 80px;
    color: #222;
    font-size: 14px;
    flex-shrink: 0;
  }

  .form-input {
    flex: 1;
    min-width: 0;
  }
}
.el-checkbox {
  margin-left: 40px;
}
</style>
