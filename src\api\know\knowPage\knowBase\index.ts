import request from '@/config/axios';

// 获取知识库
export const knowledgePage = async (params: any) => {
  return await request.get({url: '/knowledge/page', params});
};

// 删除知识库
export const knowledgeDelete = async (data: any) => {
  return await request.delete({url: `/knowledge/delete`, data});
};

// 创建知识库
export const knowledgeCreate = (data: FormData) => {
  return request.upload({url: '/knowledge/create', data});
};

// 更新知识库
export const knowledgeUpdate = (data: any) => {
  return request.put({url: `/knowledge/update`, data});
};
// 知识库上传文档
export const documentsUpload = (data: FormData) => {
  return request.post({url: '/documents/upload', data});
};
// 获取解释方式列表
export const analysisStrategyTypeList = async () => {
  return await request.get({url: '/documents/analysisStrategyTypeList'});
};
