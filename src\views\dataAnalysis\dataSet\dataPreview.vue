<template>
  <div class="data-preview-panel">
    <div class="preview-header">
      <el-button type="primary" size="small" @click="fetchPreviewTableData">刷新</el-button>
    </div>
    <el-table
      :data="tableRows"
      stripe
      border
      class="dataset-table preview-table"
      height="90%"
      max-height="90%"
      width="90%"
      :header-cell-class-name="getHeaderCellClass"
    >
      <!-- 统计周期列组 -->
      <el-table-column
        v-if="statisticalPeriodHeaders.length > 0"
        :label="`统计周期.${statisticalPeriodHeaders.length}`"
        class-name="statistical-period-header"
        align="center"
      >
        <el-table-column
          v-for="col in statisticalPeriodHeaders"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          min-width="100"
          class-name="statistical-period-column"
        />
      </el-table-column>
      <!-- 维度列组 -->
      <el-table-column
        v-if="dimensionHeaders.length > 0"
        :label="`维度.${dimensionHeaders.length}`"
        class-name="dimension-header"
        align="center"
      >
        <el-table-column
          v-for="col in dimensionHeaders"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          min-width="100"
          class-name="dimension-column"
        />
      </el-table-column>
      <!-- 指标列组 -->
      <el-table-column
        v-if="indexHeaders.length > 0"
        :label="`指标.${indexHeaders.length}`"
        class-name="indicator-header"
        align="center"
      >
        <el-table-column
          v-for="col in indexHeaders"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          min-width="100"
          class-name="indicator-column"
        />
      </el-table-column>
      <!-- 隐藏列组 -->
      <el-table-column
        v-if="hiddenHeaders.length > 0"
        :label="`隐藏.${hiddenHeaders.length}`"
        class-name="hidden-header"
        align="center"
      >
        <el-table-column
          v-for="col in hiddenHeaders"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          min-width="100"
          class-name="hidden-column"
        />
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="total > 0"
      style="margin-top: 16px; text-align: right"
      background
      :current-page="pageNo"
      :page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup>
import {ref, watch} from 'vue';
import {ElMessage} from 'element-plus';
import {postDataSetPreview} from '@/api/datainfor/dataset';
import {onMounted} from 'vue';
// ...
onMounted(() => {
  fetchPreviewTableData();
});
// 父组件传递的参数
const props = defineProps({
  tableData: {type: Array, required: true},
  getDataSourceId: {type: [String, Number], required: true},
  selectedTable: {type: Object, required: false, default: () => ({id: '', tableProp: ''})},
});

// 预览表头和数据
const statisticalPeriodHeaders = ref([]);
const dimensionHeaders = ref([]);
const indexHeaders = ref([]);
const hiddenHeaders = ref([]);
const allHeaders = ref([]);
const tableRows = ref([]);
const pageNo = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 校验
const canPreview = () => {
  return props.tableData.length > 0 && props.tableData.some(item => item.stdName && item.isIndex && item.showFieldFlag === 1);
};

// 组装参数
const getPreviewParams = () => ({
  dataSetFieldPreviewReqVOS: props.tableData.map((item, index) => ({
    id: item.id || null,
    datasetId: item.datasetId || null,
    fieldIndex: index,
    fieldName: item.name,
    fieldLabel: item.comment,
    dataType: getDataTypeNumber(item.type),
    fieldType: getFieldTypeNumber(item.isIndex),
    businessName: item.stdName,
    description: item.descriptionValue,
    dimensionOptions: item.dimensionOptionsValue,
    showFieldFlag: item.showFieldFlag !== undefined ? item.showFieldFlag : 1, // 显示状态
  })),
  datasourceId: props.getDataSourceId,
  tableNameOrSheetId: props.selectedTable?.id || props.selectedTable?.tableProp,
});

// 工具函数
function getDataTypeNumber(type) {
  const typeMap = {文本: 1, 数值: 2, 日期: 3, 日期时间: 4, 时间: 5};
  return typeMap[type] || 1;
}
function getFieldTypeNumber(type) {
  const typeMap = {指标: 1, 维度: 2, 统计周期: 3};
  return typeMap[type] || 1;
}
function parseHeaders(headerList) {
  return (headerList || []).map(obj => {
    const key = Object.keys(obj)[0];
    return {prop: key, label: obj[key]};
  });
}

// 根据字段类型和显示状态分类表头
function categorizeHeaders(tableData) {
  const statisticalPeriod = [];
  const dimension = [];
  const index = [];
  const hidden = [];

  tableData.forEach(item => {
    if (!item.stdName || !item.isIndex) return; // 跳过未配置的字段

    const headerObj = {};
    headerObj[item.name] = item.comment || item.name;

    if (item.showFieldFlag === 0) {
      // 隐藏字段
      hidden.push(headerObj);
    } else {
      // 根据字段类型分类
      switch (item.isIndex) {
        case '统计周期':
          statisticalPeriod.push(headerObj);
          break;
        case '维度':
          dimension.push(headerObj);
          break;
        case '指标':
          index.push(headerObj);
          break;
        default:
          break;
      }
    }
  });

  return {
    statisticalPeriod: parseHeaders(statisticalPeriod),
    dimension: parseHeaders(dimension),
    index: parseHeaders(index),
    hidden: parseHeaders(hidden)
  };
}

// 预览请求
async function fetchPreviewTableData(customPageNo, customPageSize) {
  if (!canPreview()) {
    // ElMessage.warning('请先为所有字段配置业务名称和维度/指标')
    return;
  }
  if (!props.selectedTable || !props.selectedTable.id && !props.selectedTable.tableProp || !props.getDataSourceId) {
    ElMessage.warning('请先选择数据源和数据表');
    return;
  }

  try {
    // 首先根据当前配置分类表头
    const categorizedHeaders = categorizeHeaders(props.tableData);
    statisticalPeriodHeaders.value = categorizedHeaders.statisticalPeriod;
    dimensionHeaders.value = categorizedHeaders.dimension;
    indexHeaders.value = categorizedHeaders.index;
    hiddenHeaders.value = categorizedHeaders.hidden;
    allHeaders.value = [...statisticalPeriodHeaders.value, ...dimensionHeaders.value, ...indexHeaders.value, ...hiddenHeaders.value];

    const params = getPreviewParams();
    params.pageNo = customPageNo || pageNo.value;
    params.pageSize = customPageSize || pageSize.value;

    const res = await postDataSetPreview(params);
    console.log('res预览情况', res);
    
    if (res) {
      // 处理数据 - 兼容现有API响应格式
      const dimensionValues = res.dimensionValueList?.list || [];
      const indexValues = res.indexValueList?.list || [];

      // 合并维度和指标数据（保持原有逻辑）
      tableRows.value = dimensionValues.map((dim, i) => ({
        ...dim,
        ...(indexValues[i] || {}),
      }));

      // 如果没有数据，尝试从indexValues获取
      if (tableRows.value.length === 0 && indexValues.length > 0) {
        tableRows.value = indexValues;
      }

      // 更新分页信息
      pageNo.value = res.dimensionValueList?.pageNo || res.indexValueList?.pageNo || 1;
      pageSize.value = res.dimensionValueList?.pageSize || res.indexValueList?.pageSize || 10;
      total.value = res.dimensionValueList?.total || res.indexValueList?.total || 0;
    } else {
      clearPreviewData();
    }
  } catch (e) {
    ElMessage.error('数据预览获取失败');
    clearPreviewData();
  }
}

// 清空预览数据
function clearPreviewData() {
  statisticalPeriodHeaders.value = [];
  dimensionHeaders.value = [];
  indexHeaders.value = [];
  hiddenHeaders.value = [];
  allHeaders.value = [];
  tableRows.value = [];
  total.value = 0;
}

// 暴露方法给父组件（可选）
defineExpose({fetchPreviewTableData});

// 监听props变化自动刷新
watch(
  () => [props.tableData, props.getDataSourceId, props.selectedTable],
  (newVal, oldVal) => {
    // 检查是否有字段配置变化（业务名称、字段类型、显示状态）
    const hasConfigChange = newVal[0] !== oldVal?.[0];
    if (hasConfigChange) {
      // 立即更新表头分类
      const categorizedHeaders = categorizeHeaders(props.tableData);
      statisticalPeriodHeaders.value = categorizedHeaders.statisticalPeriod;
      dimensionHeaders.value = categorizedHeaders.dimension;
      indexHeaders.value = categorizedHeaders.index;
      hiddenHeaders.value = categorizedHeaders.hidden;
      allHeaders.value = [...statisticalPeriodHeaders.value, ...dimensionHeaders.value, ...indexHeaders.value, ...hiddenHeaders.value];
    }

    // 自动刷新数据
    fetchPreviewTableData();
  },
  {deep: true}
);

function handlePageChange(newPage) {
  fetchPreviewTableData(newPage, pageSize.value);
}
function handleSizeChange(newSize) {
  fetchPreviewTableData(1, newSize);
}

// 动态设置表头样式类名
function getHeaderCellClass({column, columnIndex}) {
  const className = column.className || '';

  if (className.includes('statistical-period-header')) {
    return 'statistical-period-header-cell';
  } else if (className.includes('dimension-header')) {
    return 'dimension-header-cell';
  } else if (className.includes('indicator-header')) {
    return 'indicator-header-cell';
  } else if (className.includes('hidden-header')) {
    return 'hidden-header-cell';
  }

  // 根据列的父级分组来设置样式
  if (statisticalPeriodHeaders.value.length > 0 && columnIndex < statisticalPeriodHeaders.value.length) {
    return 'statistical-period-header-cell';
  } else if (dimensionHeaders.value.length > 0 && columnIndex < statisticalPeriodHeaders.value.length + dimensionHeaders.value.length) {
    return 'dimension-header-cell';
  } else if (indexHeaders.value.length > 0 && columnIndex < statisticalPeriodHeaders.value.length + dimensionHeaders.value.length + indexHeaders.value.length) {
    return 'indicator-header-cell';
  } else if (hiddenHeaders.value.length > 0) {
    return 'hidden-header-cell';
  }

  return '';
}
</script>

<style lang="scss" scoped>
.data-preview-panel {
  position: relative;
  flex: 1;
  padding-top: 40px;
}

.preview-header {
  position: absolute;
  top: 0;
  right: 0;
}

/* 表头样式 - 使用class-name选择器 */
:deep(.preview-table) {
  /* 统计周期表头样式 */
  .el-table__header-wrapper .statistical-period-header th,
  .el-table__header-wrapper th.statistical-period-header {
    background-color: #e6a23c !important;
    color: #fff !important;
    text-align: center !important;
    font-weight: bold !important;
    font-size: 16px !important;
  }

  /* 维度表头样式 */
  .el-table__header-wrapper .dimension-header th,
  .el-table__header-wrapper th.dimension-header {
    background-color: #409eff !important;
    color: #fff !important;
    text-align: center !important;
    font-weight: bold !important;
    font-size: 16px !important;
  }

  /* 指标表头样式 */
  .el-table__header-wrapper .indicator-header th,
  .el-table__header-wrapper th.indicator-header {
    background-color: #67c23a !important;
    color: #fff !important;
    text-align: center !important;
    font-weight: bold !important;
    font-size: 16px !important;
  }

  /* 隐藏表头样式 */
  .el-table__header-wrapper .hidden-header th,
  .el-table__header-wrapper th.hidden-header {
    background-color: #909399 !important;
    color: #fff !important;
    text-align: center !important;
    font-weight: bold !important;
    font-size: 16px !important;
  }
}

/* 更强的选择器 - 确保样式生效 */
:deep(.el-table) {
  .el-table__header-wrapper {
    .statistical-period-header-cell,
    th.statistical-period-header-cell {
      background-color: #e6a23c !important;
      color: #fff !important;
    }

    .dimension-header-cell,
    th.dimension-header-cell {
      background-color: #409eff !important;
      color: #fff !important;
    }

    .indicator-header-cell,
    th.indicator-header-cell {
      background-color: #67c23a !important;
      color: #fff !important;
    }

    .hidden-header-cell,
    th.hidden-header-cell {
      background-color: #909399 !important;
      color: #fff !important;
    }
  }
}

/* 最强选择器 - 直接针对表头单元格 */
:deep(.el-table__header) {
  .el-table__cell {
    text-align: center !important;
    font-weight: bold !important;
    font-size: 16px !important;
  }
}

/* 针对特定类名的表头 */
:deep(.statistical-period-header) .el-table__cell {
  background-color: #e6a23c !important;
  color: #fff !important;
}

:deep(.dimension-header) .el-table__cell {
  background-color: #409eff !important;
  color: #fff !important;
}

:deep(.indicator-header) .el-table__cell {
  background-color: #67c23a !important;
  color: #fff !important;
}

:deep(.hidden-header) .el-table__cell {
  background-color: #909399 !important;
  color: #fff !important;
}
</style>
