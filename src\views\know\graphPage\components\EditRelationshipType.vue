<template>
  <el-drawer v-model="drawer" :with-header="false" width="35%">
    <div class="drawer-header">
      <span class="drawer-header-title">编辑关系类型</span>
      <div>
        <el-button @click="closeDrawer">取消</el-button>
      </div>
    </div>
    <div class="drawer-warning">
      <el-icon color="#E6A23C"><WarningFilled /></el-icon>
      <span>注意事项：编辑关系类型后，图谱现有的关系会被统一替换，已经存在图谱中的关系不可删除</span>
    </div>
    <div class="drawer-search">
      <el-input placeholder="请输入内容" v-model="search" :prefix-icon="Search" style="width: 200px" @input="searchQueryChage"></el-input>
    </div>
    <div class="relationship-table">
      <el-table :data="tableData" style="width: 100%" max-height="50vh">
        <el-table-column prop="name" label="关系类型" align="center">
          <template #default="scope">
            <template v-if="editingRow === scope.row">
              <el-input v-model="editingForm.name" placeholder="请输入关系类型" size="small" />
            </template>
            <span v-else>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="inverseName" label="反向关系" align="center">
          <template #default="scope">
            <template v-if="editingRow === scope.row">
              <el-input v-model="editingForm.inverseName" placeholder="请输入反向关系名称" size="small" />
            </template>
            <span v-else>{{ scope.row.inverseName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="线段颜色" align="center">
          <template #default="scope">
            <template v-if="editingRow === scope.row">
              <el-color-picker v-model="editingForm.color" :predefine="predefineColors" />
            </template>
            <div v-else class="color-box" :style="{backgroundColor: scope.row.color}"></div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <template v-if="editingRow === scope.row">
              <el-button link type="primary" @click="saveEdit(scope.row)">保存</el-button>
              <el-button link @click="cancelEdit">取消</el-button>
            </template>
            <template v-else>
              <el-button link type="primary" @click="startEdit(scope.row)">编辑</el-button>
              <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
      <div class="add-relationship" @click="addNewRow">
        <el-button type="primary" link class="add-button">+ 添加</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import {ref, reactive, nextTick, computed} from 'vue';
import {WarningFilled, Search} from '@element-plus/icons-vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import * as api from '@/api/know/graph';
import {debounce} from '@/utils/tool';

const emits = defineEmits(['getKnowledgeData']);

// 状态管理
const drawer = ref(false);
const search = ref('');
const editingRow = ref<any>(null);
const editingForm = reactive<any>({name: '', inverseName: '', color: '#409EFF'});

// 分页相关
const tableData = ref<any[]>([]);
const pagination = reactive({
  currentPage: 1,
  pageSize: 5,
  total: 0,
});

const predefineColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#000000'];

const searchQueryChage = debounce(() => {
  pagination.currentPage = 1;
  getRelationTypes();
}, 300);
// 获取关系类型列表
const getRelationTypes = async () => {
  const res = await api.graphRelationshipPage({
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    name: search.value,
  });
  tableData.value = res.list;
  pagination.total = res.total;
};

// 分页事件
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getRelationTypes();
};
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  getRelationTypes();
};

// 抽屉操作
const openDrawer = async () => {
  await getRelationTypes();
  drawer.value = true;
};
const closeDrawer = () => {
  drawer.value = false;
};
// 编辑相关
const startEdit = (row: any) => {
  editingRow.value = row;
  Object.assign(editingForm, row);
};
const saveEdit = async (row: any) => {
  if (!editingForm.name.trim() || !editingForm.inverseName.trim()) {
    ElMessage.warning('请填写完整信息');
    return;
  }
  if (editingForm.id) {
    const params = {
      color: editingForm.color,
      id: row.id,
      inverseName: editingForm.inverseName,
      name: editingForm.name,
    };
    await api.editGraphRelationship(params);
    ElMessage.success('更新成功');
  } else {
    await api.createGraphRelationship(editingForm);
    ElMessage.success('创建成功');
  }
  getRelationTypes();
  emits('getKnowledgeData');
  editingRow.value = null;
};
const cancelEdit = () => {
  if (!editingForm.id) {
    tableData.value.pop(); // 新增未保存则移除
  }
  editingRow.value = null;
};
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除这条关系吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await api.deleteGraphRelationship([row.id]);
      ElMessage.success('删除成功');
      getRelationTypes();
    })
    .catch(() => {});
};
const addNewRow = () => {
  const newRow = {name: '', inverseName: '', color: '#909399'};
  tableData.value.push(newRow);
  nextTick(() => {
    startEdit(newRow);
  });
};
defineExpose({openDrawer});
</script>
<style scoped lang="scss">
.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20 20px 0px 20px;
  margin-bottom: 32px;
  .drawer-header-title {
    font-size: 16px;
    color: #76727b;
  }
}
.drawer-warning {
  display: flex;
  align-items: flex-start;
  padding: 8px 20px;
  margin-bottom: 16px;
  background-color: #fdf6ec;
  border-radius: 4px;
  font-size: 14px;
  color: #e6a23c;

  .el-icon {
    margin-right: 8px;
    margin-top: 2px;
  }
}
.drawer-search {
  display: flex;
  justify-content: flex-end;
  margin: 20px 0;
}

.relationship-table {
  margin-top: 20px;
  border: 1px solid #fff;
  border-radius: 4px;
  overflow: hidden;
}

.color-box {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin: 0 auto;
  border: 1px solid #fff;
}

.add-relationship {
  padding: 6px 0;
  margin-top: 25px;
  text-align: center;
  border: 1px dashed #dcdfe6;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f7fa;
  }

  .add-button {
    color: #409eff;
    font-size: 14px;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}

.color-picker-trigger {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
}

.el-color-picker--small .el-color-picker__trigger {
  width: 24px;
  height: 24px;
  padding: 0;
}
</style>
