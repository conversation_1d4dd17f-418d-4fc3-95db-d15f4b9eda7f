<template>
  <el-collapse v-model="activeNames" class="theme-config-collapse">
    <el-collapse-item title="标题" name="title">
      <TitleInfo ref="titleInfoRef" />
    </el-collapse-item>
    <el-collapse-item title="卡片" name="card">
      <CardInfo />
    </el-collapse-item>
    <el-collapse-item title="绘制区域" name="canvas">
      <CanvasInfo />
    </el-collapse-item>
    <el-collapse-item
      title="坐标轴"
      name="coordinate"
      v-if="showCoordinateConfig"
    >
      <CoordinateInfo />
    </el-collapse-item>
    <el-collapse-item
      title="图例"
      name="legend"
      v-if="showLegendConfig"
    >
      <LegendInfo />
    </el-collapse-item>
  </el-collapse>
</template>

<script setup>
import {ref, watch, computed} from 'vue';
import TitleInfo from './components/TitleInfo.vue';
import CardInfo from './components/CardInfo.vue';
import CanvasInfo from './components/CanvasInfo.vue';
import CoordinateInfo from './components/CoordinateInfo.vue';
import LegendInfo from './components/LegendInfo.vue';
import {useVisualStore} from '@/store/modules/visual';
const visualStore = useVisualStore();

// 控制坐标轴配置的显示
const showCoordinateConfig = computed(() => {
  const chartType = visualStore.chartType;
  // 只有柱状图、条形图、折线图显示坐标轴配置
  return ['barConfig', 'barHorizontalConfig', 'lineConfig'].includes(chartType);
});

// 控制图例配置的显示
const showLegendConfig = computed(() => {
  const chartType = visualStore.chartType;
  // 仪表图不显示图例配置
  return chartType !== 'gaugeConfig';
});

// 根据图表类型动态设置展开的面板
const activeNames = computed(() => {
  const baseNames = ['title', 'card', 'canvas'];

  // 根据图表类型添加相应的配置面板
  if (showCoordinateConfig.value) {
    baseNames.push('coordinate');
  }
  if (showLegendConfig.value) {
    baseNames.push('legend');
  }

  return baseNames;
});

const props = defineProps({
  componentName: {
    type: Object,
    default: () => ({}),
  },
});
console.log('componentName', props.componentName);
const titleInfoRef = ref(null);

// watch(
//   () => titleInfoRef?.value?.title,
//   val => {
//     console.log('titleInfoRef value1 changed:', val);
//     console.log('visualStore.chartConfig', visualStore.chartConfig);
//     visualStore.chartConfig[props.componentName].title.text = val;
//   },
//   {immediate: true}
// );
</script>

<style scoped lang="scss">
.theme-config-collapse {
  background: #fff;
  border: none;
  ::v-deep(.el-collapse-item__header) {
    font-weight: bold;
    font-size: 18px;
  }
}
</style>
