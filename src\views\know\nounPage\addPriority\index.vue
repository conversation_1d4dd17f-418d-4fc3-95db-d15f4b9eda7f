<template>
  <PageHeader title="添加名词定义">
    <template #page-header-back>
      <el-icon class="header-back-icon" @click="goBack"><ArrowLeftBold /></el-icon>
    </template>
    <template #default>
      <div class="header">
        <div>
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </div>
      </div>
    </template>
  </PageHeader>

  <el-card class="content-card">
    <div class="content">
      <div class="business-terms">
        <div class="business-terms-header">
          <span>{{ form.groupName }}</span>
        </div>
        <div class="business-terms-content">
          <div class="term-item">
            <span>{{ form.synonym }}</span>
            <el-tag type="info" :style="{background: getTagType(form.type), color: '#fff'}" disable-transitions>{{ getTagTypeText(form.type) }}</el-tag>
          </div>
          <div class="term-definition">
            <span>名词定义</span>
            <el-input type="textarea" :rows="25" v-model="form.termDefinitions"></el-input>
          </div>
        </div>
      </div>

      <div class="cards-container">
        <div class="cards-content">
          <div class="definition-card" v-for="(item, index) in definitionList" :key="index">
            <div class="card-content">
              <div class="card-title">
                <div>
                  自动切片{{ index + 1 }}
                  <span>命中切片</span>
                </div>
                <span>{{ item.score }}</span>
              </div>
              <div class="card-description">
                <span>{{ item.segment.content }}</span>
              </div>
              <div class="card-document">
                <span>{{ item.segment.documentName }}</span>
                <el-button link type="primary" @click="handleApply(item)">应用</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import {ref, onMounted, reactive} from 'vue';
import {ElMessage} from 'element-plus';
import {ArrowLeftBold} from '@element-plus/icons-vue';
import {useRouter} from 'vue-router';
import {useKnowStore} from '@/store/modules/know';
import * as api from '@/api/know/noun/index';
import PageHeader from '@/components/PageHeader.vue';

const knowStore = useKnowStore();
const router = useRouter();

const form = reactive<Record<string, any>>({
  groupName: '',
  businessName: '',
  type: '',
  groupId: '',
  id: '',
  synonym: '',
  termDefinitions: '',
});

const definitionList = ref<any[]>([]);

const getTagType = (type: any) => {
  if (type === 1) return '#96F202';
  if (type === 2) return '#155EFE';
  if (type === 3) return '#f8b160';
  if (type === 4) return '#81D3F8';
  return '#F4F4F5';
};

const getTagTypeText = (type: any) => {
  if (type === 1) return '指标';
  if (type === 2) return '维度';
  if (type === 3) return '统计周期';
  if (type === 4) return '指标集合';
  return '';
};

const getHitTheSliceList = async (): Promise<void> => {
  const res = await api.definitionsHitCutList({
    keyword: form.businessName,
  });
  definitionList.value = res;
};

// 明确函数参数类型
const handleApply = (item: any): void => {
  form.termDefinitions = item.segment.content;
};

// 错误处理明确类型
const handleSubmit = async (): Promise<void> => {
  try {
    await api.definitionsUpdate(form.id, {termDefinitions: form.termDefinitions});
    ElMessage.success('提交成功');
    router.push({name: 'Noun'});
  } catch (error: any) {
    console.error('提交失败:', error?.message || error);
  }
};

const goBack = (): void => {
  router.replace({name: 'Noun'});
  knowStore.clearNounParamsForm();
};

onMounted(() => {
  Object.keys(form).forEach(key => {
    form[key] = knowStore.nounParamsForm[key];
  });
  console.log('[ form ] >', form);
  getHitTheSliceList();
});
</script>

<style scoped lang="scss">
.header {
  display: flex;
  justify-content: end;
  align-items: center;
}

.content {
  height: 100%;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  overflow: hidden;
  flex-direction: row;
}

.business-terms {
  width: 30%;
  height: 100%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.1);
}

.business-terms-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}

.term-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  span {
    margin-right: 15px;
  }
}

.term-definition {
  margin-top: 15px;
  .el-textarea {
    margin-top: 10px;
  }
}

.cards-container {
  width: 68%;
  height: 100%;
  overflow-y: auto;
  padding: 10px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.1);
  .cards-content {
    height: 100%;
    overflow-y: auto;
  }
}

.definition-card {
  display: flex;
  margin-bottom: 15px;
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 10px;
  background: #eee;
}

.card-content {
  display: flex;
  width: 100%;
  flex-direction: column;
}

.card-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  span {
    margin-left: 15px;
  }
}

.card-description {
  margin-bottom: 10px;
  color: #606266;
}

.card-document {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
</style>
