<template>
  <div class="theme-management">
    <div class="header">
      <h2>可视化主题管理</h2>
      <el-button type="primary" @click="createNewTheme">新建主题</el-button>
    </div>

    <div class="theme-list">
      <div class="theme-grid">
        <div
          v-for="theme in themeList"
          :key="theme.id"
          class="theme-card"
          @click="handleThemeClick(theme)"
        >
          <div class="theme-preview">
            <img v-if="theme.thumbnail" :src="theme.thumbnail" alt="主题预览" />
            <div v-else class="preview-placeholder">
              <el-icon><PictureRounded /></el-icon>
              <span>预览图</span>
            </div>
          </div>
          
          <div class="theme-info">
            <h3 class="theme-name">{{ theme.name }}</h3>
            <p class="theme-description">{{ theme.description || '暂无描述' }}</p>
            <div class="theme-meta">
              <span class="create-time">{{ formatTime(theme.createTime) }}</span>
              <el-tag v-if="theme.isDefault" type="success" size="small">默认</el-tag>
            </div>
          </div>
          
          <div class="theme-actions" @click.stop>
            <el-dropdown @command="handleCommand">
              <el-button type="text" size="small">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'edit', theme}">编辑</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'copy', theme}">复制</el-dropdown-item>
                  <el-dropdown-item 
                    v-if="!theme.isDefault" 
                    :command="{action: 'delete', theme}"
                    divided
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!loading && themeList.length === 0" description="暂无主题数据">
      <el-button type="primary" @click="createNewTheme">创建第一个主题</el-button>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { PictureRounded, MoreFilled, Loading } from '@element-plus/icons-vue';
import { getThemeList, deleteTheme, type ThemeListItem } from '@/api/visualTheme';

const router = useRouter();

// 响应式数据
const themeList = ref<ThemeListItem[]>([]);
const loading = ref(false);

// 获取主题列表
const fetchThemeList = async () => {
  try {
    loading.value = true;
    const response = await getThemeList();
    
    if (response.code === 200) {
      themeList.value = response.data.list;
    } else {
      ElMessage.error(response.message || '获取主题列表失败');
    }
  } catch (error) {
    console.error('获取主题列表失败:', error);
    ElMessage.error('获取主题列表失败');
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleDateString();
};

// 新建主题
const createNewTheme = () => {
  // 跳转到可视化配置页面，使用默认配置
  router.push('/visual/visualization-config?mode=new');
};

// 点击主题卡片
const handleThemeClick = (theme: ThemeListItem) => {
  if (theme.isDefault) {
    // 点击默认主题，使用默认配置
    router.push('/visual/visualization-config?mode=new');
  } else {
    // 点击自定义主题，加载保存的配置
    router.push(`/visual/visualization-config?mode=edit&themeId=${theme.id}`);
  }
};

// 处理下拉菜单命令
const handleCommand = async (command: {action: string; theme: ThemeListItem}) => {
  const { action, theme } = command;
  
  switch (action) {
    case 'edit':
      router.push(`/visual/visualization-config?mode=edit&themeId=${theme.id}`);
      break;
      
    case 'copy':
      router.push(`/visual/visualization-config?mode=copy&themeId=${theme.id}`);
      break;
      
    case 'delete':
      await handleDeleteTheme(theme);
      break;
  }
};

// 删除主题
const handleDeleteTheme = async (theme: ThemeListItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除主题"${theme.name}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    const response = await deleteTheme(theme.id);
    
    if (response.code === 200) {
      ElMessage.success('删除成功');
      // 重新获取列表
      fetchThemeList();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除主题失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchThemeList();
});
</script>

<style scoped lang="scss">
.theme-management {
  padding: 20px;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    
    h2 {
      margin: 0;
      color: #333;
    }
  }
  
  .theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .theme-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    
    .theme-preview {
      height: 180px;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .preview-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #999;
        
        .el-icon {
          font-size: 48px;
          margin-bottom: 8px;
        }
      }
    }
    
    .theme-info {
      padding: 16px;
      
      .theme-name {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
      
      .theme-description {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #666;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .theme-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .create-time {
          font-size: 12px;
          color: #999;
        }
      }
    }
    
    .theme-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 4px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover .theme-actions {
      opacity: 1;
    }
  }
  
  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    color: #666;
    
    .el-icon {
      font-size: 32px;
      margin-bottom: 12px;
    }
  }
}
</style>
