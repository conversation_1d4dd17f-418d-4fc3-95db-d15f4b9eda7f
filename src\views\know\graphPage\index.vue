<template>
  <PageHeader title="知识图谱">
    <template #default>
      <div class="page-header-toolbar">
        <el-input v-model="searchText" placeholder="请输入搜索内容" style="width: 200px; margin-right: 12px" :prefix-icon="Search" @input="search"></el-input>
        <el-button @click="openBatchImport">批量导入</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <div class="graph-toolbar">
        <el-button @click="addEntities">添加实体</el-button>
        <el-button @click="addARelationship('')">添加关系</el-button>
        <el-dropdown placement="bottom-start">
          <el-button style="margin-left: 12px">图谱元素配置</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in dropdownList" :key="item.label" @click="graphConfiguration(item)">{{ item.label }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <EntityDrawer ref="entityDrawerRef" @addARelationship="addARelationship" @getKnowledgeData="getKnowledgeData" />
      <AddRelationDialog ref="relationDialogRef" @confirm="handleAddRelation" @getKnowledgeData="getKnowledgeData" />
      <EditRelationshipType ref="editRelationshipTypeRef" @getKnowledgeData="getKnowledgeData" />
      <EditEntitiesType ref="editEntitiesTypeRef" @getKnowledgeData="getKnowledgeData" />
      <BatchImport ref="batchImportRef" @getKnowledgeData="getKnowledgeData" />
      <div class="graph-container">
        <div ref="container" class="graph-content"></div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import {onMounted, onBeforeUnmount, ref, reactive, onUnmounted} from 'vue';
import {Search} from '@element-plus/icons-vue';
import {Graph, NodeEvent} from '@antv/g6';
import PageHeader from '@/components/PageHeader.vue';
import EntityDrawer from './components/EntityDrawer.vue';
import AddRelationDialog from './components/AddRelationDialog.vue';
import EditRelationshipType from './components/EditRelationshipType.vue';
import EditEntitiesType from './components/EditEntitiesType.vue';
import BatchImport from './components/BatchImport.vue';
import * as api from '@/api/know/graph/index';
import {debounce} from '@/utils/tool';

const searchText = ref('');
const container = ref(null);
const entityDrawerRef = ref<InstanceType<typeof EntityDrawer> | null>(null);
const relationDialogRef = ref<InstanceType<typeof AddRelationDialog> | null>(null);
const editRelationshipTypeRef = ref<InstanceType<typeof EditRelationshipType> | null>(null);
const editEntitiesTypeRef = ref<InstanceType<typeof EditEntitiesType> | null>(null);
const batchImportRef = ref<InstanceType<typeof BatchImport> | null>(null);

let graph: any = null;
const knowledgeData = reactive<any>({
  nodes: [],
  edges: [],
});
const dropdownList = [{label: '编辑关系类型'}, {label: '编辑实体类型'}];

onMounted(() => {
  getKnowledgeData();
  document.querySelector('.graph-content').addEventListener('contextmenu', function (event) {
    event.preventDefault(); // 阻止默认的右键点击事件
  });
});

onUnmounted(() => {
  document.querySelector('.graph-content').removeEventListener('contextmenu', function (event) {
    event.preventDefault(); // 阻止默认的右键点击事件
  });
});

const getKnowledgeData = async () => {
  try {
    const res = await api.graphDisplay();
    knowledgeData.nodes = res.nodeList.map(item => {
      return {
        ...item,
        id: item.id.toString(),
      };
    });
    knowledgeData.edges = res.relationshipList.map(edge => ({
      ...edge,
      id: edge.id.toString(),
      source: edge.startNodeId.toString(),
      target: edge.endNodeId.toString(),
    }));
  } catch (error) {
    console.log('error:', error);
  } finally {
    initGraph();
  }
};

const initGraph = () => {
  if (graph) {
    graph.destroy();
  }
  graph = new Graph({
    container: container.value,
    width: container.value.clientWidth,
    height: container.value.clientHeight,
    layout: {
      type: 'antv-dagre',
      rankdir: 'LR',
      nodesep: 50, // 节点之间的间距
      ranksep: 50, // 层之间的间距
      sortByCombo: true, // 是否按组合排序
    },
    // 启用画布操作
    // zoom-canvas 缩放画布
    // drag-canvas 拖拽画布
    // drag-element 拖拽节点
    // hover-activate 鼠标悬停激活
    behaviors: ['zoom-canvas', 'drag-canvas', 'drag-element', 'hover-activate'],
    // 节点样式
    node: {
      style: {
        size: 60,
        labelText: (d: any) => d.name,
        fill: (d: any): string => d.color,
        stroke: (d: any): string => d.color,
        fillOpacity: 0.2,
        lineWidth: 1,
        labelPlacement: 'center',
        labelFill: '#000',
        labelWordWrap: true,
        labelMaxLines: 3,
        labelTextOverflow: 'ellipsis',
        labelMaxWidth: '70%',
      },
      state: {
        selected: {
          stroke: (d: any): string => d.color,
          lineWidth: 2,
        },
      },
    },
    // 连线样式
    edge: {
      style: {
        labelText: (d: any): string => d.typeName,
        stroke: (d: any): string => d.color,
        labelOffsetY: -20,
      },
    },
    // 插件配置
    plugins: [
      {
        type: 'minimap',
        key: 'minimap', // 唯一标识
        size: [200, 120], // 小地图宽高
        padding: 10, // 小地图边距
        position: 'bottom-left', 
      },
    ],
  });

  graph.setData(knowledgeData);
  graph.render();

  graph.on(NodeEvent.CLICK, evt => {
    const {targetType, target} = evt;
    knowledgeData.nodes.forEach(item => {
      if (item.id === target.id) {
        entityDrawerRef.value?.open('details', item);
      }
    });
  });
};

const search = debounce(async () => {
  const nodeId = knowledgeData.nodes.find(item => item.name === searchText.value)?.id;

  // 清除所有节点的选中状态
  knowledgeData.nodes.forEach(node => {
    if (node.id === nodeId) {
      graph.setElementState(node.id, 'selected', false);
    } else {
      graph.setElementState(node.id);
    }
  });

  // 聚焦并选中
  await graph.focusElement(nodeId, {
    duration: 1000,
    easing: 'ease-in-out',
  });
}, 1000);

const addEntities = () => {
  entityDrawerRef.value?.open('add');
};

const addARelationship = (nodeName: string) => {
  relationDialogRef.value?.open(nodeName);
};

const save = () => {};

const handleAddRelation = (relationData: any) => {
  const sourceId = knowledgeData.nodes.find((item: any) => item.label == relationData.startEntity).id;

  const targetId = knowledgeData.nodes.find((item: any) => item.label == relationData.endEntity).id;
  // 生成唯一ID
  const edgeId = `edge${Date.now()}`;
  // 添加新关系到图谱数据
  knowledgeData.edges.push({
    id: edgeId,
    source: sourceId,
    target: targetId,
    relationType: relationData.relation,
    color: 'red',
  });
  // 刷新图谱
  graph.setData(knowledgeData);
  graph.layout();
  graph.render();
};

const graphConfiguration = (item: any) => {
  switch (item.label) {
    case '编辑关系类型':
      editRelationshipTypeRef.value?.openDrawer();
      break;
    case '编辑实体类型':
      editEntitiesTypeRef.value?.openDrawer();
      break;
  }
};

const openBatchImport = () => {
  batchImportRef.value?.open();
};

onBeforeUnmount(() => {
  if (graph) {
    graph.destroy();
    graph = null;
  }
});
</script>

<style scoped lang="scss">
.page-header-toolbar {
  display: flex;
  justify-content: end;
}
.graph-toolbar {
  width: 100%;
  height: 7%;
}
.graph-container {
  position: relative;
  width: 100%;
  height: 93%;
  border: 1px solid #ccc;
}
.graph-content {
  width: 100%;
  height: 100%;
}
</style>
