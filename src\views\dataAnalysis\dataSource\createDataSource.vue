<template>
  <div class="maskLayer">
    <div class="outerLayer">
      <div class="content">
        <div class="leftList">
          <el-collapse v-model="activeNames" @change="collapseHandleChange">
            <el-collapse-item title="本地文件" name="1">
              <div :class="item.name == collapseActive ? 'collapseItem isActive' : 'collapseItem'"
                v-for="item in collapseFileList" @click="dataBaseClick(item, 'file')">
                <img :src="item.value" style="width: 20px" />
                {{ item.name }}
              </div>
            </el-collapse-item>
            <el-collapse-item title="数据库" name="2">
              <div :class="item.name == collapseActive ? 'collapseItem isActive' : 'collapseItem'"
                v-for="item in dataBaseList" @click="dataBaseClick(item, 'dataBase')">
                <img :src="item.value" style="width: 20px" />
                {{ item.name }}
              </div>
            </el-collapse-item>
            <el-collapse-item title="API" name="3">
              <div :class="item.name == collapseActive ? 'collapseItem isActive' : 'collapseItem'"
                v-for="item in collapseApiList" @click="dataBaseClick(item, 'API')">
                <img :src="item.value" style="width: 20px" />
                {{ item.name }}
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="chiropractic">
          <!-- 未选择 -->
          <div class="noStatusSelected" v-if="initialTip == 'init'">
            <img style="width: 103px; height: 100px" src="@/assets/imgs/datadempement/folder.png" alt="" />
            <p class="universal-font-color">请选择左侧数据源列表查看详情</p>

            <div class="tipText">
              <span class="universal-font-color" style="text-align: center">
                <el-icon style="color: #fa9600">
                  <WarningFilled />
                </el-icon>
                目前仅支持3种数据库类型、本地表格文件，
                <br />
                其他类型数据源暂未开放
              </span>
            </div>
          </div>
          <!-- 数据库Mysql -->
          <!-- <createDataBase v-if="initialTip == 'dataBase'" :prop="databaseType" /> -->
          <!-- 文件上传 -->
          <div class="fileUpload" v-if="initialTip == 'file'">
            <div style="display: flex; width: 100%">
              <span style="font-size: 18px">文件上传</span>
              <el-steps style="flex: 0.5; margin-left: 25%; margin-top: 20px" :active="stepsActive"
                process-status="finish" finish-status="success">
                <el-step title="文件上传" />
                <el-step title="预览数据" />
              </el-steps>
            </div>
            <!-- 第一个步骤 -->
            <div class="fileUploadContent" v-if="stepsActive == 0">
              <el-form ref="fileUploadRef" class="fileUploadForm" :model="fileUploadForm" label-width="auto"
                :rules="fileUploadRules" label-position="top">
                <el-form-item label="数据源名称" prop="dataName">
                  <el-input v-model.number="fileUploadForm.dataName" type="text" autocomplete="off" />
                </el-form-item>
              </el-form>
              <el-upload style="width: 100%" action="#" :on-change="handleChange" :file-list="fileList" drag multiple
                :limit="1" :auto-upload="false" :on-progress="handleProgress" :show-file-list="true">
                <el-icon class="el-icon--upload"><img style="width: 48px; height: 44px"
                    src="@/assets/imgs/datadempement/uploadArea.png" mode="scaleToFill" /></el-icon>
                <div class="el-upload__text" style="font-weight: 400; font-size: 14px">
                  <em>点击</em>
                  将文件拖到此处次区域上传
                </div>
                <div class="el-upload__text" style="font-size: 12px; color: #999">文件只支持.csv、.xlsx、.xls格式</div>
                <template #file="{ file }">
                  <div class="upload-fileList">
                    <div class="upload-fileList-info">
                      <img src="@/assets/imgs/datadempement/uploadFile.png" alt="" class="upload-fileList-img" />
                      <span class="file-name">{{ file.name }}</span>
                      <span class="file-size">{{ formatFileSize(file.size) }}</span>
                    </div>
                    <div class="file-actions">
                      <el-icon class="delete-icon" @click.stop="handleDeleteFile(file)"><svg-icon
                          name="close" /></el-icon>
                    </div>
                  </div>
                </template>
              </el-upload>
              <div></div>
              <div style="width: 100%; margin-top: 50px">
                <span style="display: inline-flex; align-items: center; gap: 4px; font-size: 16px">
                  温馨提示
                  <el-icon style="color: #0d6ce4">
                    <WarningFilled />
                  </el-icon>
                </span>
                <ul style="list-style: none">
                  <li>1.仅支持上传结构化数据，以便于我们更好的识别。有合并的单元格的，请处理后再上传</li>
                  <li>2.系统会默认将上传的文件首行作为标题行，第二行开始作为要上传的数据</li>
                  <li>3.若上传的表格Sheet页首行为空或者整体内容为空会上传失败</li>
                  <li>4.最多支持5个Sheet的解析和上传，若您需要上传超过5个Sheet的内容，请拆分为多个Excel文件</li>
                  <li>5.文件大小不超过50 MB</li>
                </ul>
              </div>

              <div style="position: absolute; bottom: 20px; right: 20px; display: flex; justify-content: flex-end">
                <el-button type="primary" @click="uploadNext(fileUploadRef)">下一步</el-button>
              </div>
            </div>
            <!-- 第二个步骤 -->
            <div class="fileUploadContentSecond" v-if="stepsActive == 1">
              <div class="sheetoptionsBody">
                <div class="sheetTabs" :style="{ background: acitveSheet == item.id ? '#fff' : '' }"
                  v-for="item in sheetList" :key="item.sheetName" @click="acitveSheetClick(item, $event)">
                  <el-checkbox :value="item.checkedValue" v-model="item.checked"></el-checkbox>
                  <span class="sheetTabsName" :style="{ color: acitveSheet == item.id ? '#2744d6' : '' }">{{
                    item.sheetName }}</span>
                </div>
              </div>
              <GeneralTables :columns="previewColumns" :data="tableData" :border="false" :stripe="false"
                :pagination="false" :header-cell-style="headerCellStyle" :table-key="tableKey" style="margin-top: 20px">
                <template v-for="(column, index) in previewColumns" :key="`${column.prop}-${index}`"
                  #[`header-${column.prop}`]>
                  <div style="display: flex; align-items: center; gap: 5px">
                    <el-select v-model="column.selectValue" placeholder="请选择" style="width: 80px"
                      @change="value => handleSelectChange(column, value)">
                      <el-option v-for="selectItem in fileTypeList" :key="selectItem.label" :label="selectItem.label"
                        :value="selectItem.label"></el-option>
                    </el-select>
                    <el-input v-model="column.inputValue" :readonly="column.readonly" @dblclick="toggleEditable(column)"
                      style="width: 50%"></el-input>
                  </div>
                </template>
              </GeneralTables>
              <div style="position: absolute; bottom: 20px; right: 20px; display: flex; justify-content: flex-end">
                <el-button type="primary" @click="okAndUpload(fileUploadRef)">确定并上传</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onBeforeMount, onMounted, computed } from 'vue';
import SvgIcon from '@/components/SvgIcon.vue';
import createDataBase from './createDataBase.vue';
import { ElMessage } from 'element-plus';
import { Close, WarningFilled } from '@element-plus/icons-vue'

import mysql from '@/assets/imgs/datadempement/mysql.png';
import kingBase from '@/assets/imgs/datadempement/kingBase.png';
import dermal from '@/assets/imgs/datadempement/dermal.png';
import GeneralTables from '@/components/GeneralTables/index.vue';
import excel from '@/assets/imgs/datadempement/excel.png';
import API from '@/assets/imgs/datadempement/API.png';

const emit = defineEmits(['close']);
const activeNames = ref(['1']); // 默认展开的面板
let initialTip = ref('init');
let databaseType = ref('');
let dataBaseList = ref([
  {
    name: 'MySql',
    value: mysql,
  },
  {
    name: 'Clickhouse',
    value: kingBase,
  },
  {
    name: 'Gauss',
    value: dermal,
  },
]);
console.log(dataBaseList.value, '----');

let collapseFileList = ref([
  {
    name: '本地文件',
    value: excel,
  },
]);
let collapseApiList = ref([
  {
    name: 'API数据',
    value: API,
  },
]);
let collapseActive = ref('');
// --------数据库----------
const mySqlFormRef = ref(null);
const mySqlRules = reactive({
  dataName: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  address: [{ required: true, message: '请输入数据库地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  theNameOfTheDatabase: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  version: [{ required: true, message: '请选择数据库版本', trigger: 'change' }],
});
let mySqlForm = reactive({
  dataName: '',
  address: '',
  port: '',
  theNameOfTheDatabase: '',
  userName: '',
  password: '',
  version: '',
});
let versionList = ref([{ value: '5.5' }, { value: '5.6' }, { value: '5.7' }, { value: '8.0' }]);
// ------文件上传第一步----------
let stepsActive = ref(0);
let fileUploadRef = ref(null);
let fileUploadForm = reactive({
  dataName: '',
});
let fileList = ref([]);
let fileUploadRules = reactive({
  dataName: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
});
const uploadRef = ref(null);

// ------文件上传第二步-------------

let sheetList = ref([
  {
    id: '1',
    sheetName: '生产经营汇总表-sheet1',
    checked: true,
    checkedValue: '1',

    errorMsg: '',
    // 预览数据表格列配置
    previewColumns: [
      {
        prop: 'date',
        label: 'Date',
        align: 'center',
        headerSlot: true,
        selectValue: '数值',
        inputValue: '111',
        readonly: true,
      },
      {
        prop: 'name',
        label: 'Name',
        align: 'center',
        headerSlot: true,
        selectValue: '数值',
        inputValue: '111',
        readonly: true,
      },
      {
        prop: 'address',
        label: 'Address',
        align: 'center',
        headerSlot: true,
        selectValue: '数值',
        inputValue: '111',
        readonly: true,
      },
    ],
    tableData: [
      {
        date: '2016-05-03',
        name: 'Tom',
        address: 'No. 189, Grove St, Los Angeles',
      },
      {
        date: '2016-05-02',
        name: 'Tom',
        address: 'No. 189, Grove St, Los Angeles',
      },
    ],
  },
  {
    id: '2',
    sheetName: '生产经营汇总表-sheet2',
    checked: true,
    checkedValue: '2',
    errorMsg: '该sheep页数据为空',
    previewColumns: [
      {
        prop: 'date',
        label: 'Date',
        align: 'center',
        headerSlot: true,
        selectValue: '日期',
        inputValue: '222',
        readonly: true,
      },
      {
        prop: 'name',
        label: 'Name',
        align: 'center',
        headerSlot: true,
        selectValue: '日期',
        inputValue: '222',
        readonly: true,
      },
      {
        prop: 'address',
        label: 'Address',
        align: 'center',
        headerSlot: true,
        selectValue: '日期',
        inputValue: '222',
        readonly: true,
      },
    ],
    tableData: [
      {
        date: '2016-05-04',
        name: 'Tom1',
        address: 'No. 189, Grove St, Los Angeles',
      },
      {
        date: '2016-05-01',
        name: 'Tom1',
        address: 'No. 189, Grove St, Los Angeles',
      },
    ],
  },
  {
    id: '3',
    sheetName: '生产经营汇总表-sheet3',
    checked: true,
    checkedValue: '3',
    errorMsg: '该sheep页数据为空',
    previewColumns: [
      {
        prop: 'date',
        label: 'Date',
        align: 'center',
        headerSlot: true,
        selectValue: '文本',
        inputValue: '333',
        readonly: true,
      },
      {
        prop: 'name',
        label: 'Name',
        align: 'center',
        headerSlot: true,
        selectValue: '文本',
        readonly: true,
        inputValue: '333',
      },
      {
        prop: 'address',
        label: 'Address',
        align: 'center',
        headerSlot: true,
        selectValue: '文本',
        readonly: true,
        inputValue: '333',
      },
    ],
    tableData: [
      {
        date: '2016-06-04',
        name: 'Tom3',
        address: 'No. 189, Grove St, Los Angeles',
      },
      {
        date: '2016-06-01',
        name: 'Tom3',
        address: 'No. 189, Grove St, Los Angeles',
      },
    ],
  },
]);
let fileTypeList = ref([
  {
    label: '数值',
  },
  {
    label: '日期',
  },
  {
    label: '文本',
  },
]);
let acitveSheet = ref('1');
const uploadTableData = ref([
  {
    date: '2016-05-03',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
]);

// 添加一个ref来存储当前激活的列配置
const currentColumns = ref([]);
const tableKey = ref(0);

// 修改 previewColumns 计算属性
const previewColumns = computed(() => {
  const activeSheetData = sheetList.value.find(sheet => sheet.id === acitveSheet.value);
  if (!activeSheetData) return [];

  // 只在第一次或切换sheet时更新currentColumns
  if (currentColumns.value.length === 0 || currentColumns.value[0]?.sheetId !== activeSheetData.id) {
    // 从sheetList中获取当前sheet的列配置
    currentColumns.value = activeSheetData.previewColumns.map(column => ({
      ...column,
      sheetId: activeSheetData.id,
      prop: column.columnProp,
      label: column.label,
      align: column.align || 'center',
      headerSlot: true,
      selectValue: column.selectValue || '',
      inputValue: column.inputValue || '',
      readonly: column.readonly !== undefined ? column.readonly : true,
    }));
  }

  return currentColumns.value;
});

// Add computed property for tableData
const tableData = computed(() => {
  const activeSheetData = sheetList.value.find(sheet => sheet.id === acitveSheet.value);
  return activeSheetData ? activeSheetData.tableData : [];
});

// 表头样式
const headerCellStyle = {
  backgroundColor: '#f5f7fa',
  color: '#303133',
  padding: '8px 0',
};

const dataBaseClick = (item, tips) => {
  console.log(item, '----', tips);
  if (tips == 'dataBase') {
    databaseType.value = item.name;
    console.log(databaseType.value, '----');

  }
  initialTip.value = tips;
  collapseActive.value = item.name;
  mySqlFormRef?.value?.resetFields();
};

// 文件上传前的校验
const beforeUpload = file => {
  const isCSV = file.type === 'text/csv';
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isCSV && !isExcel) {
    ElMessage.error('只能上传 CSV、XLSX、XLS 文件!');
    return false;
  }
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过 50MB!');
    return false;
  }
  return true;
};
const handleChange = (file, uploadFiles) => {
  console.log('文件选择变化', file, uploadFiles); // 调试：检查是否触发
  fileList.value = uploadFiles; // 直接使用传入的 uploadFiles 更新响应式引用
};
// 上传进度回调
const handleProgress = (event, file) => {
  uploadProgress.value = Math.round(event.percent);
};

onMounted(() => {
  console.log('3.-组件挂载到页面之后执行-------onMounted')
  console.log('dataBaseList:', dataBaseList.value);
  console.log('activeNames:', activeNames.value);
});

const close = () => {
  emit('close'); // 触发关闭事件
};
const collapseHandleChange = () => { 
  console.log(collapseActive.value, '----');

};
const uploadNext = async formEl => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (fileList.value.length === 0) {
        ElMessage.warning('请先选择要上传的文件');
        return;
      }

      // 获取当前文件
      const currentFile = fileList.value[0];

      // 手动调用 beforeUpload 方法进行校验
      if (!beforeUpload(currentFile.raw)) {
        return;
      }

      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', currentFile.raw);
      formData.append('dataName', fileUploadForm.dataName);
      console.log(fileList.vlaue);

      try {
        // 这里添加实际的文件上传API调用
        // const response = await axios.post('/api/upload', formData);

        // 模拟上传成功
        ElMessage.success('文件上传成功');
        stepsActive.value = 1;
      } catch (error) {
        ElMessage.error('文件上传失败，请重试');
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};
const okAndUpload = () => {
  console.log(sheetList.value, '----');

  if (stepsActive.value++ > 2) stepsActive.value = 0;
  emit('close'); // 触发关闭事件
};

const acitveSheetClick = (item, event) => {
  // 在切换sheet之前，保存当前sheet的修改到sheetList
  if (currentColumns.value.length > 0) {
    const currentSheetId = currentColumns.value[0]?.sheetId;
    const currentSheet = sheetList.value.find(sheet => sheet.id === currentSheetId);
    if (currentSheet) {
      currentSheet.previewColumns = currentColumns.value.map(column => ({
        ...column,
        selectValue: column.selectValue,
        inputValue: column.inputValue,
        readonly: column.readonly,
      }));
    }
  }

  acitveSheet.value = item.id;
  // 清空currentColumns，强制在下次计算时更新
  currentColumns.value = [];
  // 增加 tableKey 触发表格刷新
  tableKey.value++;
};
const toggleEditable = column => {
  const targetColumn = currentColumns.value.find(col => col.prop === column.prop);
  if (targetColumn) {
    targetColumn.readonly = !targetColumn.readonly;

    // 同步更新到 sheetList
    const currentSheet = sheetList.value.find(sheet => sheet.id === acitveSheet.value);
    if (currentSheet) {
      const sheetColumn = currentSheet.previewColumns.find(col => col.prop === column.prop);
      if (sheetColumn) {
        sheetColumn.readonly = !sheetColumn.readonly;
      }
    }
  }
};
const handleSelectChange = (column, value) => {
  const targetColumn = currentColumns.value.find(col => col.prop === column.prop);
  if (targetColumn) {
    targetColumn.selectValue = value;

    // 同步更新到 sheetList
    const currentSheet = sheetList.value.find(sheet => sheet.id === acitveSheet.value);
    if (currentSheet) {
      const sheetColumn = currentSheet.previewColumns.find(col => col.prop === column.prop);
      if (sheetColumn) {
        sheetColumn.selectValue = value;
      }
    }
  }
};
// 上传的文件大小
const formatFileSize = size => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

const handleDeleteFile = file => {
  const index = fileList.value.indexOf(file);
  fileList.value.splice(index, 1);
};

// 使用toRefs解构
// let { } = { ...toRefs(data) }

defineExpose({});
</script>
<style scoped lang="scss">
.universal-font-color {
  color: #999999;
}

.maskLayer {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 1003;

  .outerLayer {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 1005;
    width: 100vw;
    height: 90vh;
    background: #f5f7fa;
    padding: 20px 15px 10px 15px;
    border-radius: 15px 15px 0 0;

    .content {
      width: 100%;
      height: calc(100% - 40px);
      display: flex;
      flex-direction: row;

      .leftList {
        width: 300px;
        height: 100%;
        padding: 0 10px 0 0;

        .el-collapse {
          width: 100%;
          flex: 1;
          overflow-y: auto;
          border: none;
          scrollbar-gutter: stable;

          /* 保证滚动条出现时不会改变布局 */
          :deep(.el-collapse-item) {
            border-radius: 10px !important;
            border: 1px solid #eee !important;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
          }

          :deep(.el-collapse-item__header) {
            background-color: #fff;
            border: none;
          }

          :deep(.el-collapse-item__content) {
            background-color: #fff;
            padding-bottom: 0;
          }

          :deep(.el-collapse-item__wrap) {
            border: none;
            background-color: #fff;

            .collapseItem {
              width: 100%;
              margin: auto;
              padding: 15px;
              margin-top: 10px;
              border-radius: 10px;
              background: #f5f7fa;
              color: #666;
              border: 1px solid #f5f7fa;

              .details-title {
                font-weight: 700;
                font-size: 14px;
              }

              .details {
                display: flex;
                justify-content: space-between;
                flex-wrap: nowrap;
                font-size: 12px;
              }
            }

            .isActive {
              border-color: #c6d0f7;
              background: #e7ecf9;
              color: #2744d6;
            }
          }
        }

        .collapseItem {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          padding: 10px;
          border-radius: 10px;
          background: #fff;

          img {
            margin-right: 10px;
          }
        }
      }

      .chiropractic {
        width: calc(100vw - 330px);
        height: 100%;
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        position: relative;
        border: 1px solid #eeeeee;

        .noStatusSelected {
          width: 740px;
          height: 320px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border: 1px dashed #ccc;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          /* 水平和垂直居中 */
          .tipText {
            padding: 16px;
            display: flex;
            align-items: center;
            background-color: #f5f7fa;
            border-radius: 10px;
          }
        }

        :deep(.el-scrollbar) {
          z-index: 99999;
        }

        .fileUpload {
          height: 100%;
          overflow: auto;

          :deep(.el-setp) {
            position: relative;
          }

          :deep(.el-step__line) {
            width: 78%;
            margin-left: auto;
            margin-right: 10px;
          }

          :deep(.el-step__main) {
            width: 70px;
            position: absolute;
            top: -6px;
            left: 27px;
          }

          :deep(.is-finish .el-step__icon.is-text) {
            color: #fff;
            background: #2744d6;
            border-color: #2744d6;
          }

          :deep(.el-step__title.is-finish) {
            color: #2744d6;
          }

          :deep(.is-success .el-step__icon.is-text) {
            color: #2744d6;
            border-color: #2744d6;
          }

          :deep(.el-step__title.is-success) {
            color: #2744d6;
          }

          .fileUploadContent {
            max-width: 60%;
            margin: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            :deep(.el-upload-dragger) {
              background: #eff2ff;
            }

            .upload-fileList {
              padding: 8px 12px;
              background-color: #f5f7fa;
              border-radius: 8px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin: 8px 0;
              transition: all 0.3s;

              &:hover {
                background-color: #eef1f6;
              }

              .upload-fileList-info {
                display: flex;
                align-items: center;
                flex: 1;
                min-width: 0; // 防止文件名过长导致布局问题

                .upload-fileList-img {
                  width: 36px;
                  height: 44px;
                  margin-right: 12px;
                  flex-shrink: 0;
                }

                .file-name {
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  color: #606266;
                  margin-right: 12px;
                }

                .file-size {
                  color: #909399;
                  font-size: 13px;
                  flex-shrink: 0;
                }
              }

              .file-actions {
                margin-left: 12px;

                .delete-icon {
                  cursor: pointer;
                  color: #909399;
                  transition: color 0.3s;

                  &:hover {
                    color: #f56c6c;
                  }
                }
              }
            }

            .upload-info {
              margin-top: 10px;
              padding: 10px;
              background-color: #f5f7fa;
              border-radius: 4px;
              color: #606266;
            }

            .fileUploadForm {
              width: 100%;
              margin: auto;
              margin-top: 50px;
            }

            ul {
              padding: 0px;

              li {
                margin: 5px 0;
                font-size: 14px;
                color: #333;
              }
            }
          }

          .fileUploadContentSecond {
            margin: auto;
            display: flex;
            flex-direction: column;

            :deep(.el-select) {
              width: auto;
              min-width: 80px !important;
            }

            :deep(.el-select__wrapper) {
              box-shadow: none;
            }

            .sheetoptionsBody {
              width: fit-content;
              display: flex;
              align-items: center;
              justify-content: space-evenly;
              margin: auto;
              margin-top: 50px;
              background: #f5f7fa;
              padding: 4px;
              border-radius: 10px;

              .sheetTabs {
                width: 230px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 6px;

                .sheetTabsName {
                  color: #999;
                  font-size: 14px;
                }
              }
            }

            .sheetisActive {
              width: 100%;
              height: 6px;
              border-radius: 5px;
              margin-bottom: 5px;
            }
          }      }
      }
    }
  }
}
</style>
