<template>
  <el-container class="app-container">
    <el-container>
      <el-aside :width="sidebarWidth" class="sidebar-transition">
        <Sidebar />
      </el-aside>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import {ref} from 'vue';
import Sidebar from '@/Layouts/Sidebar.vue';

const sidebarWidth = ref('250px');
</script>

<style>
.app-container {
  height: 100vh;
}

.sidebar-transition {
  transition: width 0.3s ease;
}
</style>
