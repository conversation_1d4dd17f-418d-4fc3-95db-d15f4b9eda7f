import type {RouteRecordRaw} from 'vue-router';

export const qnTestRoutes: RouteRecordRaw[] = [
  {
    path: '/qntest',
    children: [
      {
        path: 'addqntest',
        name: 'AddQnTest',
        component: () => import('@/views/qnTest/addQnTest/index.vue'),
        meta: {
          title: '添加测试',
          menu: false,
          isActive: '/qntest/qntestlist',
        },
      },
      {
        path: 'qntestlist',
        name: 'QnTestList',
        component: () => import('@/views/qnTest/qnTestList/index.vue'),
      },
    ],
  },
];
