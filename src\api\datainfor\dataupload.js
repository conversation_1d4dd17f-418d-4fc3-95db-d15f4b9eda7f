import request from '@/config/axios'
import { getAccessToken } from '@/utils/auth'

// 上传数据源excel文件接口
export function dataUpload(dataSaveFile) {
  return request.upload({
    url: '/datasource/excel/upload',
    headers: {
      'Authorization': 'Bearer ' + getAccessToken()
    },
    data: dataSaveFile
  })
}

// 数据源excel保存接口
export function dataSave(data) {
  return request.post({
    url: '/datasource/excel/create',
    data
  })
}



// 测试数据库连接情况
export function testDatabaseConnection(data) {
  return request.post({
    url: 'datasource/database/testConnection',
    data
  })
}
// 保存数据库
export function saveDatabase(data) {
  return request.post({
    url: 'datasource/database/create',
    data
  })
}
// 获取数据源列表
export function getDataSourceList(data) {
  return request.get({
    url: 'datasource/list',
    params: data
  })
}
// 获取数据源详情
export function getDataSourceDetail(id) {
  return request.get({
    url: `/datasource/${id}`,
  })
}

// 获取数据源及关联数据表信息
export function getDataSourceWithTables(id, data) {
  return request.get({
    url: `/datasource/dataSourceAndDataTableInfo/${id}`,
    params: data
  })
}

// 获取字段详情列表(字段详情预览)
// /basic/datasource/database/dataPreview/fieldDetail
export function getFieldDetail(data) {
  return request.get({
    url: '/datasource/database/dataPreview/fieldDetail',
    params: data
  })
}

// 获取表格数据预览列表(数据预览)
// /basic/datasource/database/dataPreview/dataDetail
export function getDataDetail(data) {
  return request.get({
    url: '/datasource/database/dataPreview/dataDetail',
    params: data
  })
}
// 删除数据源
export function deleteDataSource(id) {
  return request.delete({
    url: `/datasource/${id}`,
  })
}

// 修改数据源
export function updateDataSource(data) {
  return request.put({
    url: '/datasource',
    data
  })
}

// 获取数据库类型数据源详情
// basic/datasource/database/get?id=2507021036140000

/* 

{
	"code": 0,
	"message": "",
	"result": {
		"createTime": "",
		"creator": "",
		"id": "",
		"name": "",
		"type": 0,
		"typeId": ""
	},
	"success": true,
	"timestamp": 0
}

*/
export function getDatabaseDetail(id) {
  return request.get({
    url: '/datasource/database/get',
    params: { id }
  })
}


// 获取数据库详细配置信息
export function getDatabaseConfig(id) {
  return request.get({
    url: `/datasource/database/get?id=${id}`,
  })
}

// 修改数据库类型数据源
export function updateDatabase(data) {
  return request.put({
    url: '/datasource/database/update',
    data
  })
}