{"root": ["./src/app.vue", "./src/main.ts", "./src/vite-env.d.ts", "./src/api/know/knowbase/index.ts", "./src/api/know/knowdetail/index.ts", "./src/api/know/noun/index.ts", "./src/api/qntest/addqntest/index.ts", "./src/api/qntest/qntest/index.ts", "./src/config/axios/config.ts", "./src/config/axios/errorcode.ts", "./src/config/axios/index.ts", "./src/config/axios/service.ts", "./src/hooks/web/usecache.ts", "./src/router/index.ts", "./src/store/index.ts", "./src/store/modules/qntest.ts", "./src/utils/auth.ts", "./src/utils/jsencrypt.ts", "./src/views/know/knowpage/knowdetail/index.vue", "./src/views/know/knowpage/knowdetail/components/hittesting.vue", "./src/views/know/knowpage/knowdetail/components/slicedetails.vue", "./src/views/know/knowpage/knowbase/index.vue", "./src/views/know/knowpage/knowbase/components/createknowbase.vue", "./src/views/know/nounpage/addpriority/index.vue", "./src/views/know/nounpage/noun/index.vue", "./src/views/know/nounpage/noun/components/createnoun.vue", "./src/views/know/nounpage/noun/components/createnoungroup.vue", "./src/views/know/nounpage/noun/components/relationship.vue", "./src/views/know/nounpage/noun/components/setpriority.vue", "./src/views/qntest/addqntest/index.vue", "./src/views/qntest/qntestlist/index.vue", "./src/views/qntest/qntestlist/components/addinbulkdialog.vue"], "errors": true, "version": "5.8.3"}