<template>
  <div class="special-rules-config">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">{{ currentRow?.name || '数据集' }} 特殊规则</h2>
        <p class="page-description">为当前数据集配置特殊规则，用于数据处理和查询优化</p>
      </div>
      <div class="header-right">
        <el-button
          v-if="hasValidRules"
          type="primary"
          size="large"
          @click="saveAllRules"
          :loading="saving"
          class="save-btn"
        >
          对此数据集保存规则
        </el-button>
      </div>
    </div>

    <!-- 规则配置表格 -->
    <div class="rules-table-container">
      <div class="table-header">
        <span class="header-title">规则内容</span>
        <span class="header-actions">操作</span>

      </div>


      <!-- 规则列表 -->
      <div class="rules-list">

        <div v-for="(rule, index) in rulesList" :key="rule.id" class="rule-row">
          <div class="rule-content">
            <el-input v-if="rule.isEditing" v-model="rule.content" type="textarea" :rows="2" placeholder="请输入规则内容..."
              class="rule-input" @blur="saveRule(index)" @keyup.enter="saveRule(index)" />
            <div v-else class="rule-text" @click="editRule(index)">
              {{ rule.content || '点击编辑规则内容...' }}
            </div>
          </div>
          <div class="rule-actions">
            <el-button v-if="!rule.isEditing" type="primary" size="small" @click="editRule(index)" :icon="Edit">
              编辑
            </el-button>
            <el-button v-if="rule.isEditing" type="success" size="small" @click="saveRule(index)" :icon="Check">
              保存
            </el-button>
            <el-button type="danger" size="small" @click="deleteRule(index)" :icon="Delete">
              删除
            </el-button>
          </div>
        </div>

        <!-- 添加新规则按钮 -->
        <div class="add-rule-row">
          <el-button type="primary" :icon="Plus" @click="addRule" class="add-rule-btn" dashed>
            添加规则
          </el-button>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { updateSpecialRules } from '@/api/datainfor/dataset.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Edit, Delete, Plus, Check } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  currentRow: {
    type: Object,
    default: () => null
  }
});

// 响应式数据
const rulesList = ref([]);
const saving = ref(false);
let ruleIdCounter = 0;

// 计算属性
const hasValidRules = computed(() => {
  return rulesList.value.some(rule => rule.content && rule.content.trim());
});

// 生成唯一ID
const generateRuleId = () => {
  return `rule_${Date.now()}_${++ruleIdCounter}`;
};

// 添加规则
const addRule = () => {
  const newRule = {
    id: generateRuleId(),
    content: '',
    isEditing: true
  };
  rulesList.value.push(newRule);
};

// 编辑规则
const editRule = (index) => {
  rulesList.value[index].isEditing = true;
};

// 保存规则
const saveRule = (index) => {
  const rule = rulesList.value[index];
  if (!rule.content || !rule.content.trim()) {
    ElMessage.warning('规则内容不能为空');
    return;
  }
  rule.isEditing = false;
  ElMessage.success('规则保存成功');
};

// 删除规则
const deleteRule = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条规则吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    rulesList.value.splice(index, 1);
    ElMessage.success('规则删除成功');
  } catch {
    // 用户取消删除
  }
};

// 保存所有规则
const saveAllRules = async () => {
  try {
    if (!props.currentRow?.id) {
      ElMessage.error('缺少数据集ID，无法保存规则');
      return;
    }

    // 检查是否有正在编辑的规则
    const editingRules = rulesList.value.filter(rule => rule.isEditing);
    if (editingRules.length > 0) {
      ElMessage.warning('请先保存正在编辑的规则');
      return;
    }

    // 过滤出有效的规则内容
    const validRules = rulesList.value
      .filter(rule => rule.content && rule.content.trim())
      .map(rule => rule.content.trim());

    if (validRules.length === 0) {
      ElMessage.warning('请至少添加一条有效规则');
      return;
    }

    saving.value = true;

    const params = {
      dataSetId: props.currentRow.id,
      specialRules: validRules
    };

    console.log('保存特殊规则参数:', params);

    const res = await updateSpecialRules(params);
    console.log('updateSpecialRules API响应:', res);

    ElMessage.success('特殊规则保存成功');

  } catch (error) {
    console.error('保存特殊规则失败:', error);
    ElMessage.error('保存特殊规则失败');
  } finally {
    saving.value = false;
  }
};

// 初始化
onMounted(() => {
  // 默认添加一条空规则
  addRule();

  console.log('特殊规则配置组件初始化');
  console.log('当前数据集信息:', props.currentRow);
});
</script>

<style lang="scss" scoped>
.special-rules-config {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;

  .header-left {
    flex: 1;
  }

  .header-right {
    margin-left: 24px;
    display: flex;
    align-items: center;
  }

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 8px 0;
    letter-spacing: 0.5px;
  }

  .page-description {
    font-size: 14px;
    color: #909399;
    margin: 0;
    line-height: 1.6;
  }

  .save-btn {
    width: 200px;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.rules-table-container {
  margin-bottom: 32px;

  .table-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: #f5f7fa;
    border-radius: 8px 8px 0 0;
    border: 1px solid #ebeef5;
    font-weight: 600;
    color: #606266;

    .header-title {
      flex: 1;
    }

    .header-actions {
      width: 200px;
      text-align: center;
    }
  }
}

.rules-list {
  border: 1px solid #ebeef5;
  border-top: none;
  border-radius: 0 0 8px 8px;
}

.rule-row {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.2s;

  &:hover {
    background: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }

  .rule-content {
    flex: 1;
    margin-right: 20px;

    .rule-input {
      width: 100%;
    }

    .rule-text {
      min-height: 60px;
      padding: 8px 12px;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      cursor: pointer;
      color: #606266;
      line-height: 1.5;
      transition: all 0.2s;

      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }

      &:empty::before {
        content: '点击编辑规则内容...';
        color: #c0c4cc;
      }
    }
  }

  .rule-actions {
    width: 200px;
    display: flex;
    justify-content: center;
    gap: 8px;
  }
}

.add-rule-row {
  padding: 20px;
  text-align: center;
  background: #fafbfc;

  .add-rule-btn {
    width: 200px;
    height: 40px;
    border: 2px dashed #d9d9d9;
    background: transparent;
    color: #606266;

    &:hover {
      border-color: #409eff;
      color: #409eff;
      background: #f0f9ff;
    }
  }
}


</style>
