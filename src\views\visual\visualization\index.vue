<template>
  <PageHeader title="可视化主题管理">
    <template #default>
      <div class="content-header">
        <el-input placeholder="搜索主题" :prefix-icon="Search" class="search-input" />
        <el-button type="primary" class="create-btn" @click="newlyBuiltTopic">新建主题</el-button>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <!-- 折叠面板 -->
      <el-collapse v-model="activeNames">
        <!-- 我创建的主题 -->
        <el-collapse-item name="1">
          <template #title>
            <span>我创建的</span>
            <span class="count">({{ myCreated.length }})</span>
          </template>
          <div class="scroll-container">
            <!-- 添加滚动容器 -->
            <el-row :gutter="20" class="card-list">
              <el-col class="card-item" :span="4" v-for="(item, idx) in myCreated" :key="idx">
                <el-card class="theme-card">
                  <!-- 问数样式状态标识 -->
                  <div class="ask-number-badge" v-if="item.askNumberFlag">
                    <el-icon class="star-icon"><StarFilled /></el-icon>
                  </div>
                  <div class="cover" ><img  @click="cardClick(item)" src="https://vue.mtruning.club/static/png/moke-20211219181327.png" alt="主题" /></div>
                  <template #footer>
                    <div class="card-info">
                      <div class="title">
                        {{ item.name }}
                        <el-dropdown @command="(command) => handleSelect(command, item)">
                          <span class="el-dropdown-link">
                            <SvgIcon name="icon/threePoints" size="20" color="black" />
                          </span>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item v-for="menuItem in getDropdownList(item)" :key="menuItem.value" :command="menuItem">{{ menuItem.label }}</el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <div class="meta">描述：{{ item.description || '暂无描述' }}</div>
                      <div class="meta">更新时间：{{ formatTime(item.updateTime) }}</div>
                      <el-dropdown trigger="hover" class="dropdown">
                        <i class="el-icon-more" />
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item>查看详情</el-dropdown-item>
                            <el-dropdown-item>申请编辑</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </template>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>

        <!-- 授权给我的主题 -->
        <el-collapse-item name="2">
          <template #title>
            <span>授权给我的</span>
            <span class="count">({{ authorizedList.length }})</span>
          </template>
          <div class="scroll-container">
            <!-- 添加滚动容器 -->
            <el-row :gutter="20" class="card-list">
              <el-col :span="4" v-for="(item, idx) in authorizedList" :key="idx" class="card-item">
                <el-card class="theme-card" @click="handleAuthorizedCardClick(item)">
                  <div class="cover">
                    <img src="https://vue.mtruning.club/static/png/moke-20211219181327.png" alt="主题" />
                  </div>
                  <template #footer>
                    <div class="card-info">
                      <div class="title">
                        {{ item.title }}
                        <el-dropdown>
                          <span class="el-dropdown-link">
                            <SvgIcon name="icon/threePoints" size="20" color="black" />
                          </span>
                          <template #dropdown>
                            <el-dropdown-menu>
                              <el-dropdown-item>查看详情</el-dropdown-item>
                              <el-dropdown-item>申请编辑</el-dropdown-item>
                            </el-dropdown-menu>
                          </template>
                        </el-dropdown>
                      </div>
                      <div class="meta">所有者：{{ item.owner }}</div>
                      <div class="meta">更新时间：{{ item.updateTime }}</div>
                      <el-dropdown trigger="hover" class="dropdown">
                        <i class="el-icon-more" />
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item>查看详情</el-dropdown-item>
                            <el-dropdown-item>申请编辑</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </template>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import {reactive, ref, onMounted} from 'vue';
import {useRouter} from 'vue-router';
import {Search, StarFilled} from '@element-plus/icons-vue';
import PageHeader from '@/components/PageHeader.vue';
import SvgIcon from '@/components/SvgIcon.vue';
import { getThemeList, deleteTheme, setAsAskNumberStyle, type ThemeListItem } from '@/api/visualTheme';
import { ElMessage, ElMessageBox } from 'element-plus';

const router = useRouter();

const activeNames = ref(['1', '2']);

// 我创建的主题列表
const myCreated = ref<ThemeListItem[]>([]);

// 加载状态
const loading = ref(false);

const authorizedList = reactive([
  {
    title: '国家能源绿色清洁主题',
    owner: '张大千',
    updateTime: '2025/02/03 18:20:23',
  },
  {
    title: '简约白色',
    owner: '张大千',
    updateTime: '2025/02/03 18:20:23',
  },
  {
    title: '深邃黑色',
    owner: '张大千',
    updateTime: '2025/02/03 18:20:23',
  },
  {
    title: '绿色科技风',
    owner: '张大千',
    updateTime: '2025/02/03 18:20:23',
  },
  {
    title: '深邃黑色',
    owner: '张大千',
    updateTime: '2025/02/03 18:20:23',
  },
  {
    title: '绿色科技风',
    owner: '张大千',
    updateTime: '2025/02/03 18:20:23',
  },
]);
// 基础下拉菜单选项
const baseDropdownList = ref([
  {
    label: '权限配置',
    value: '2',
  },
  {
    label: '编辑',
    value: '3',
  },
  {
    label: '删除',
    value: '4',
  },
]);

// 根据主题的问数样式状态获取下拉菜单选项
const getDropdownList = (theme: ThemeListItem) => {
  const askNumberItem = {
    label: theme.askNumberFlag ? '取消问数样式' : '设为问数样式',
    value: '1',
  };

  // 将问数样式选项放在第一位
  return [askNumberItem, ...baseDropdownList.value];
};

// 获取我创建的主题列表
const fetchMyCreatedThemes = async () => {
  try {
    loading.value = true;
    const response = await getThemeList();

    if (response.code === 200) {
      // 过滤出非默认主题（用户创建的主题）
      myCreated.value = response.data.list.filter(theme => !theme.isDefault);
    } else {
      ElMessage.error(response.message || '获取主题列表失败');
    }
  } catch (error) {
    console.error('获取主题列表失败:', error);
    ElMessage.error('获取主题列表失败');
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 新建主题
const newlyBuiltTopic = () => {
  // 跳转到配置页面，使用默认配置
  router.push('/visual/visualization-config?mode=new');
};

// 处理下拉菜单选择
const handleSelect = async (menuItem: any, theme: ThemeListItem) => {
  switch (menuItem.value) {
    case '1': // 设为问数样式
      await handleSetAsAskNumberStyle(theme);
      break;
    case '2': // 权限配置
      ElMessage.info('权限配置功能待实现');
      break;
    case '3': // 编辑
      router.push(`/visual/visualization-config?mode=edit&themeId=${theme.id}`);
      break;
    case '4': // 删除
      handleDeleteTheme(theme);
      break;
  }
};

// 设置或取消问数样式
const handleSetAsAskNumberStyle = async (theme: ThemeListItem) => {
  try {
    // 根据当前状态决定是设置还是取消
    const newFlag = !theme.askNumberFlag;
    const response = await setAsAskNumberStyle(theme.id, newFlag);

    if (response.code === 200) {
      ElMessage.success(newFlag ? '已成功设置为问数样式' : '已成功取消问数样式');
      // 刷新列表
      fetchMyCreatedThemes();
    } else {
      ElMessage.error(response.message || (newFlag ? '设置失败' : '取消失败'));
    }
  } catch (error) {
    console.error('操作问数样式失败:', error);
    ElMessage.error('操作失败');
  }
};

// 删除主题
const handleDeleteTheme = async (theme: ThemeListItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除主题"${theme.name}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const response = await deleteTheme(theme.id);

    if (response.code === 200) {
      ElMessage.success('删除成功');
      // 重新获取列表
      fetchMyCreatedThemes();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除主题失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 我创建的主题卡片点击事件
const cardClick = (item: ThemeListItem) => {
  // 点击卡片进入编辑模式，加载保存的配置
  router.push(`/visual/visualization-config?mode=edit&themeId=${item.id}`);
};

// 授权主题卡片点击事件
const handleAuthorizedCardClick = (item: any) => {
  // 授权主题只能查看，跳转到配置页面但不能编辑
  ElMessage.info('授权主题功能待实现');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchMyCreatedThemes();
});
</script>

<style lang="scss" scoped>
// 折叠面板样式
.el-collapse {
  border: none;

  // 折叠面板标题
  :deep(.el-collapse-item__header) {
    font-size: 16px;
    font-weight: 500;
    padding-left: 0;
    .count {
      margin-left: 8px;
      color: #999;
    }
  }
  :deep(.el-collapse-item__wrap) {
    border-bottom: none;
  }
  // 折叠面板内容
  .el-collapse-item__content {
    padding: 0;
    margin-top: 12px;
  }

  // 滚动容器样式
  .scroll-container {
    height: 330px; // 可根据需求调整高度
    overflow-y: auto; // 纵向滚动
    padding-bottom: 12px; // 预留空间避免滚动条遮挡内容

    // 滚动条美化
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  // 卡片列表样式
  .card-list {
    width: 100%;
    .card-item {
      margin-bottom: 12px;
      .theme-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;

        // 问数样式状态标识
        .ask-number-badge {
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 10;
          background: rgba(255, 193, 7, 0.9);
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          .star-icon {
            color: #fff;
            font-size: 14px;
          }
        }

        .cover {
          height: 120px;
          background: #f5f7fa;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #999;
          font-size: 14px;
          img {
            width: 100%;
            height: 100%;
            cursor: pointer;
          }
        }

        .card-info {
          padding: 10px;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
          }

          .meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          }

          .dropdown {
            align-self: flex-end;
            cursor: pointer;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
