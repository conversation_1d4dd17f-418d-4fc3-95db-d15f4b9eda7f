<template>
  <div class="form-row">
    <label class="form-label">标题</label>
    <el-input
      class="form-input"
      placeholder="请输入标题"
      v-model="visualStore.chartConfig[visualStore.chartType].title.text"
    />
  </div>

  <div class="form-row">
    <label class="form-label">标题字体</label>
    <div class="text-style-toolbar">
      <!-- 字体选择 -->
      <el-select
        v-model="currentFontFamily"
        class="font-select"
        placeholder="选择字体"
        size="small"
        @change="handleFontFamilyChange"
      >
        <el-option label="Arial" value="Arial" />
        <el-option label="Times New Roman" value="Times New Roman" />
        <el-option label="Microsoft YaHei" value="Microsoft YaHei" />
        <el-option label="SimSun" value="SimSun" />
        <el-option label="SimHei" value="SimHei" />
      </el-select>

      <!-- 字号选择 -->
      <el-input-number
        v-model="visualStore.chartConfig[visualStore.chartType].title.textStyle.fontSize"
        class="font-size-input"
        :min="8"
        :max="72"
        size="small"
        controls-position="right"
      />

      <!-- 样式按钮组 -->
      <div class="style-buttons">
        <!-- 加粗按钮 -->
        <el-button
          :type="isBold ? 'primary' : 'default'"
          size="small"
          class="style-btn bold-btn"
          @click="toggleBold"
        >
          <strong>B</strong>
        </el-button>

        <!-- 斜体按钮 -->
        <el-button
          :type="isItalic ? 'primary' : 'default'"
          size="small"
          class="style-btn italic-btn"
          @click="toggleItalic"
        >
          <em>I</em>
        </el-button>
      </div>

      <!-- 对齐方式按钮组 -->
      <div class="align-buttons">
        <el-button
          :type="titleAlign === 'left' ? 'primary' : 'default'"
          size="small"
          class="align-btn"
          @click="setAlign('left')"
          title="左对齐"
        >
          <svg class="icon" viewBox="0 0 1024 1024">
            <path d="M128 192h768v64H128zM128 384h512v64H128zM128 576h768v64H128zM128 768h512v64H128z"/>
          </svg>
        </el-button>

        <el-button
          :type="titleAlign === 'center' ? 'primary' : 'default'"
          size="small"
          class="align-btn"
          @click="setAlign('center')"
          title="居中对齐"
        >
          <svg class="icon" viewBox="0 0 1024 1024">
            <path d="M128 192h768v64H128zM256 384h512v64H256zM128 576h768v64H128zM256 768h512v64H256z"/>
          </svg>
        </el-button>

        <el-button
          :type="titleAlign === 'right' ? 'primary' : 'default'"
          size="small"
          class="align-btn"
          @click="setAlign('right')"
          title="右对齐"
        >
          <svg class="icon" viewBox="0 0 1024 1024">
            <path d="M128 192h768v64H128zM384 384h512v64H384zM128 576h768v64H128zM384 768h512v64H384z"/>
          </svg>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, toRaw } from "vue";
import { useVisualStore } from "@/store/modules/visual";

const title = ref("11");
defineExpose({
  title: toRaw(title),
});

const visualStore = useVisualStore();

// 当前字体系列的计算属性
const currentFontFamily = computed({
  get: () => {
    const textStyle = visualStore.chartConfig[visualStore.chartType].title.textStyle;
    return textStyle.fontFamily || 'Arial';
  },
  set: (value: string) => {
    const textStyle = visualStore.chartConfig[visualStore.chartType].title.textStyle;
    textStyle.fontFamily = value;
  }
});

// 是否加粗的计算属性
const isBold = computed(() => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].title.textStyle;
  return textStyle.fontWeight === 'bold' || textStyle.fontWeight === 700;
});

// 是否斜体的计算属性
const isItalic = computed(() => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].title.textStyle;
  return textStyle.fontStyle === 'italic';
});

// 标题对齐方式的计算属性
const titleAlign = computed({
  get: () => {
    return visualStore.chartConfig[visualStore.chartType].title.left || 'center';
  },
  set: (value: string) => {
    visualStore.chartConfig[visualStore.chartType].title.left = value;
  }
});

// 处理字体系列变化
const handleFontFamilyChange = (value: string) => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].title.textStyle;
  textStyle.fontFamily = value;
};

// 切换加粗
const toggleBold = () => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].title.textStyle;
  if (textStyle.fontWeight === 'bold' || textStyle.fontWeight === 700) {
    textStyle.fontWeight = 'normal';
  } else {
    textStyle.fontWeight = 'bold';
  }
};

// 切换斜体
const toggleItalic = () => {
  const textStyle = visualStore.chartConfig[visualStore.chartType].title.textStyle;
  if (textStyle.fontStyle === 'italic') {
    textStyle.fontStyle = 'normal';
  } else {
    textStyle.fontStyle = 'italic';
  }
};

// 设置对齐方式
const setAlign = (align: string) => {
  visualStore.chartConfig[visualStore.chartType].title.left = align;
};
</script>

<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18px;

  .form-label {
    width: 80px;
    color: #222;
    font-size: 14px;
    flex-shrink: 0;
    line-height: 32px; // 与输入框高度对齐
  }

  .form-input {
    flex: 1;
    min-width: 0;
  }
}

.text-style-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;

  .font-select {
    width: 140px;
  }

  .font-size-input {
    width: 80px;

    :deep(.el-input__inner) {
      text-align: center;
    }
  }

  .style-buttons {
    display: flex;
    gap: 4px;

    .style-btn {
      width: 32px;
      height: 32px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      &.bold-btn strong {
        font-size: 14px;
        font-weight: 700;
      }

      &.italic-btn em {
        font-size: 14px;
        font-style: italic;
      }
    }
  }

  .align-buttons {
    display: flex;
    gap: 4px;
    margin-left: 8px;

    .align-btn {
      width: 32px;
      height: 32px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
    }
  }
}

.icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

// 响应式布局
@media (max-width: 768px) {
  .text-style-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .font-select {
      width: 100%;
    }

    .style-buttons,
    .align-buttons {
      width: 100%;
      justify-content: flex-start;
    }
  }
}
</style>
