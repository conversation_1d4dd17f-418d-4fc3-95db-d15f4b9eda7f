import request from '@/config/axios'

// 获取表格数据
export const askNumberPage = async (params: any) => {
  return await request.get({ url: '/datamanage/ask-number/page', params })
}
// 删除/批量删除
export const askNumberDelete = async (data: any) => {
  return await request.delete({
    url: `/datamanage/ask-number/delete`,
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
// 批量添加上传
export const askNumberCreateBatch = async (data: any) => {
  return await request.post({
    url: `/datamanage/ask-number/createBatch`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
