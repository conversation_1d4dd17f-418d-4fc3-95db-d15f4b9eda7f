<template>
  <el-dialog v-model="dialogVisible" title="编辑同义词" width="400px" :before-close="handleClose">
    <!-- 名词分组展示 -->
    <div class="synonym-dialog__group">
      <span class="label">名词分组：</span>
      <span class="value">{{ businessName }}</span>
    </div>

    <!-- 业务名词表单 -->
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" class="synonym-dialog__form">
      <el-form-item label="业务名词" prop="synonym">
        <el-input type="textarea" v-model="form.synonyms" placeholder="多个同义词请用“,”进行分隔" :rows="3" show-word-limit />
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="synonym-dialog__footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, reactive} from 'vue';
import type {FormInstance} from 'element-plus';
import * as api from '@/api/know/noun/index';

const emit = defineEmits(['resetPagination']);

// 弹窗显隐控制
const dialogVisible = ref(false);

// 表单数据
const form = reactive({
  id: '',
  synonyms: '', // 业务名词（逗号分隔）
});

// 表单校验规则
const rules = {
  synonyms: [{required: true, message: '请输入业务名词', trigger: 'blur'}],
};

// 表单引用（用于校验）
const formRef = ref<FormInstance>();

// 名词分组（父组件传入）
const businessName = ref('');

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false;
}

function handleSave() {
  formRef.value?.validate(async valid => {
    if (valid) {
      // 去除首尾空格，触发保存事件
      try {
        await api.definitionsUpdate(form.id, { synonyms: form.synonyms });
        emit('resetPagination'); // 重置分页
      } catch (error) {}
      dialogVisible.value = false;
    }
  });
}

// 暴露给父组件的方法：打开弹窗（传入名词分组和初始同义词）
function open(rowData: any) {
  businessName.value = rowData.businessName;
  form.synonyms = rowData.synonyms;
  form.id = rowData.id;
  dialogVisible.value = true;
}

// 对外暴露方法
defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.synonym-dialog {
  &__group {
    margin-bottom: 16px;
    .label {
      font-weight: 500;
      margin-right: 8px;
    }
    .value {
      color: #666;
    }
  }

  &__form {
    margin-bottom: 12px;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    :deep .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid #ebeef5;
    }
  }
}

/* 调整文本域样式 */
:deep .el-textarea {
  width: 100%;
  .el-textarea__inner {
    padding: 8px;
    resize: none; // 禁止拉伸
  }
}
</style>
