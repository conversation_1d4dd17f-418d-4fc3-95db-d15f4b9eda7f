import {service} from './service';

import {config} from './config';

const {default_headers} = config;

const request = (option: any) => {
  const {headersType, headers, ...otherOption} = option;
  return service({
    ...otherOption,
    headers: {
      'Content-Type': headersType || default_headers,
      ...headers,
    },
  });
};
export default {
  get: async <T = any>(option: any) => {
    const res: any = await request({method: 'GET', ...option});
    return res.result as T;
  },
  post: async <T = any>(option: any) => {
    const  res: any =await request({method: 'POST', ...option});
    return res.result as unknown as T;
  },
  postOriginal: async (option: any) => {
    const  res: any =await request({method: 'POST', ...option});
    return res;
  },
  delete: async <T = any>(option: any) => {
    const  res: any =await request({method: 'DELETE', ...option});
    return res.result as unknown as T;
  },
  put: async <T = any>(option: any) => {
    const  res: any =await request({method: 'PUT', ...option});
    return res.result as unknown as T;
  },
  download: async <T = any>(option: any) => {
    const  res: any =await request({method: 'GET', responseType: 'blob', ...option});
    return res as unknown as Promise<T>;
  },
  upload: async <T = any>(option: any) => {
    option.headersType = 'multipart/form-data';
    const  res: any =await request({method: 'POST', ...option});
    return res as unknown as Promise<T>;
  },
};
