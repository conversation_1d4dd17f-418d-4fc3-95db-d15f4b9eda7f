<template>
  <!-- 分组菜单 -->
  <el-sub-menu v-if="route.meta?.grouping && route.children?.length" :index="fullPath">
    <template #title>
      <el-icon>
        <SvgIcon :name="route.meta?.icon" size="20" :color="isGroupActive ? '#409EFF' : ''" />
      </el-icon>
      <span>{{ route.meta?.title || route.name }}</span>
    </template>

    <!-- 子菜单递归渲染 -->
    <SidebarItem v-for="child in route.children.filter(r => r.meta?.menu)" :key="child.path" :route="child" :basePath="fullPath" />
  </el-sub-menu>

  <!-- 普通菜单项 -->
  <el-menu-item v-else :index="fullPath" v-if="route.meta?.menu">
    <el-icon>
      <SvgIcon :name="route.meta?.icon" size="20" :color="isActive ? '#409EFF' : ''" />
    </el-icon>
    <span>{{ route.meta?.title || route.name }}</span>
  </el-menu-item>
</template>
<script setup>
import {computed, inject} from 'vue';
import {useRoute} from 'vue-router';
import SvgIcon from '@/components/SvgIcon.vue';

const props = defineProps({
  route: Object,
  basePath: {
    type: String,
    default: '',
  },
});

const routeCurrent = useRoute();
const activeMenu = inject('activeMenu');

const fullPath = computed(() => {
  const childPath = props.route.path || '';
  if (childPath.startsWith('/')) return childPath;
  return props.basePath.endsWith('/') ? props.basePath + childPath : props.basePath + '/' + childPath;
});

const isActive = computed(() => {
  return activeMenu?.value === fullPath.value;
});

const isGroupActive = computed(() => {
  if (activeMenu?.value === fullPath.value) return true;
  if (!props.route.children) return false;
  return props.route.children.some(child => {
    const childPath = props.basePath.endsWith('/') ? props.basePath + child.path : props.basePath + '/' + child.path;
    return activeMenu?.value.includes(childPath);
  });
});
</script>
