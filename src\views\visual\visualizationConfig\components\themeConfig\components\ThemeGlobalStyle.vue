<template>
  <div>
    <div class="form-row">
      <label class="form-label">页面字体</label>
      <el-select class="form-input" placeholder="请选择">
        <el-option label="默认字体" value="default" />
      </el-select>
    </div>
    <div class="form-row">
      <label class="form-label">组件圆角</label>
      <el-radio-group class="radio-group" v-model="visualStore.pageConfig.globalStyle.componentBorderRadius">
        <el-radio v-for="item in roundOptions" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
      </el-radio-group>
    </div>
    <div class="form-row">
      <label class="form-label">组件间距</label>
      <el-radio-group class="radio-group" v-model="visualStore.pageConfig.globalStyle.componentSpacing">
        <el-radio v-for="item in spaceOptions" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>
<script setup>
import {ref} from 'vue';
import {useVisualStore} from '@/store/modules/visual';
const visualStore = useVisualStore();

const roundOptions = [
  {label: '无', value: '0px'},
  {label: '小', value: '8px'},
  {label: '大', value: '20px'},
];
const spaceOptions = [
  {label: '紧凑', value: '5px'},
  {label: '常规', value: '10px'},
  {label: '宽泛', value: '20px'},
];
</script>
<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}
.form-label {
  width: 80px;
  color: #222;
  font-size: 14px;
  flex-shrink: 0;
}
.form-input {
  flex: 1;
  min-width: 0;
}
.radio-group {
  margin-left: 8px;
}
</style>
