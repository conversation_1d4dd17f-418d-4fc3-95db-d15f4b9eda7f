import {createApp} from 'vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import App from './App.vue';
import router from './router';
// 引入全局样式
import '@/styles/index.scss';
// 引入状态管理
import {setupStore} from '@/store';

// 创建应用实例并链式调用
const app = createApp(App);
app.use(router);
setupStore(app);
app.use(ElementPlus, {
  locale: zhCn,
});
app.mount('#app');
