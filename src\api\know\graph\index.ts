import request from '@/config/axios';
// 获取图谱数据（图谱展示页面数据获取）
export const graphDisplay = async () => {
  return await request.get({url: `/knowledge/graph/display`});
};
// 删除图谱节点
export const graphDeleteNodes = async (data: any) => {
  return await request.delete({url: `/knowledge/graph/deleteNodes`, data});
};
// 删除图谱边
export const graphDeleteRelationships = async (data: any) => {
  return await request.delete({url: `/knowledge/graph/deleteRelationships`, data});
};

// 创建节点（添加实体保存）
export const graphCreateNode = async (data: any) => {
  return await request.post({url: `/knowledge/graph/createNode`, data});
};
// 更新节点（编辑实体保存）
export const graphUpdateNode = async (data: any) => {
  return await request.put({url: `/knowledge/graph/updateNode`, data});
};
// 创建边（添加关系保存）
export const graphCreateRelationship = async (data: any) => {
  return await request.post({url: `/knowledge/graph/createRelationship`, data});
};

// 获取实体类型表格（编辑关系类型）
export const graphNodePage = async (params?: any) => {
  return await request.get({url: `/knowledge/graph/node/page`, params});
};

// 创建实体（编辑关系类型）
export const createGraphNode = async (data: any) => {
  return await request.post({url: `/knowledge/graph/node`, data});
};

// 编辑实体（编辑关系类型）
export const updateGraphNode = async (data: any) => {
  return await request.put({url: `/knowledge/graph/node`, data});
};

// 删除实体（编辑关系类型）
export const deleteGraphNode = async (data: any) => {
  return await request.delete({url: `/knowledge/graph/node`, data});
};

// 获取关系类型表格（编辑关系类型）
export const graphRelationshipPage = async (params?: any) => {
  return await request.get({url: `/knowledge/graph/relationship/page`, params});
};

// 创建关系类型（编辑关系类型）
export const createGraphRelationship = async (data: any) => {
  return await request.post({url: `/knowledge/graph/relationship`, data});
};

// 编辑关系类型（编辑关系类型）
export const editGraphRelationship = async (data: any) => {
  return await request.put({url: `/knowledge/graph/relationship`, data});
};

// 删除关系类型（编辑关系类型）
export const deleteGraphRelationship = async (data: any) => {
  return await request.delete({url: `/knowledge/graph/relationship`, data});
};

// 获取节点关系列表（添加实体关系表格）
export const nodeRelationList = async (id: any) => {
  return await request.get({url: `/knowledge/graph/${id}`});
};

// 图谱导入
export const graphImport = async (data: FormData) => {
  return await request.post({url: `/knowledge/graph/import`, data, headers: {'Content-Type': 'multipart/form-data'}});
};
