import request from '@/config/axios';

// 查询工具配置
export const configPageQuery = async (data: any) => {
  return await request.post({url: '/base/tool/config/pageQuery', data});
};
// 创建工具配置
export const configCreate = async (data: any) => {
  return await request.post({
    url: `/base/tool/config/create`,
    data,
    headers: {'Content-Type': 'application/json'},
  });
};
// 创建工具配置
export const configUpdate = async (data: any) => {
  return await request.post({
    url: `/base/tool/config/update`,
    data,
  });
};

// 删除工具配置
export const configDeleteById = async (id: any) => {
  return await request.get({
    url: `/base/tool/config/deleteById/${id}`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
};
