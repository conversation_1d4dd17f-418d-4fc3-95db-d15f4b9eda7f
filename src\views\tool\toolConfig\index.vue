<template>
  <PageHeader title="工具配置">
    <template #default>
      <div class="content-header">
        <div></div>
        <el-button type="primary" class="create-btn" @click="addTool">添加</el-button>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <el-table :data="tableData" style="width: 100%" max-height="630px">
        <el-table-column v-for="col in columns" :key="col.prop" :prop="col.prop" :label="col.label"></el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="scope">
            <div class="operation">
              <el-button link type="primary" @click="editLibrary(scope.row)">编辑</el-button>
              <el-button link type="danger" @click="deleteLibrary(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="content-footer">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </div>
  </el-card>
  <ToolDialog ref="toolDialogRef" @resetPagination="resetPagination" />
</template>

<script setup lang="ts">
import {ref, reactive, onMounted} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import PageHeader from '@/components/PageHeader.vue';
import ToolDialog from './components/ToolDialog.vue';
import * as api from '@/api/tool/index';

const toolDialogRef = ref();
const tableData = ref([]);
const columns = [
  {prop: 'toolChName', label: '工具中文名称（全称）'},
  {prop: 'toolAbbreviationChName', label: '工具中文名称（简称）'},
  {prop: 'toolEnName', label: '工具英文名称（全称）'},
  {prop: 'toolAbbreviationEnName', label: '工具英文名称（简称）'},
  {prop: 'toolDesc', label: '工具描述'},
  {prop: 'toolRequiredField', label: '必须计算字段'},
  {prop: 'toolFormula', label: '计算公式'},
  {prop: 'toolLogic', label: '计算逻辑'},
  {prop: 'toolPurpose', label: '工具使用目的'},
  {prop: 'toolScene', label: '常用应用场景'},
];

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

onMounted(() => {
  getTableData();
});

const getTableData = () => {
  api
    .configPageQuery({
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
    })
    .then(res => {
      tableData.value = res.records;
      pagination.total = res.total;
    })
    .catch(err => {
      console.error('获取数据失败:', err);
    });
};

const resetPagination = () => {
  pagination.currentPage = 1;
  pagination.pageSize = 10;
  getTableData();
};

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1; // 切换每页数量时重置为第一页
  getTableData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getTableData();
};

const addTool = () => {
  toolDialogRef.value.openDialog('add');
};

const editLibrary = (row: any) => {
  toolDialogRef.value.openDialog('edit', row);
};

const deleteLibrary = (row: any) => {
  ElMessageBox.confirm('确定要删除这条数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await api.configDeleteById(row.id);
      resetPagination();
      ElMessage.success('删除成功');
    })
    .catch(() => {});
};
</script>

<style scoped lang="scss">
.content-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
