div
<template>
  <el-dialog v-model="visible" width="360px" class="safe-dialog" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
    <template #header>
      <div class="dialog-header">
        <span>名词</span>
        <el-icon @click="close" class="close-icon"><Close /></el-icon>
      </div>
    </template>

    <div class="dialog-body">
      <!-- 名词部分 -->
      <div class="section">
        <div class="title">名词</div>
        <div class="content">
          安全监测
          <el-tag type="info" class="tag">指标集合</el-tag>
        </div>
      </div>

      <!-- 定义部分 -->
      <div class="section">
        <div class="section"></div>
        <div class="title">定义</div>
        <div class="content">安全检测情况包括，设备检测、环境监测、和人员定位。三个方面。</div>
      </div>

      <!-- 关联关系 -->
      <div class="section">
        <div class="title">关联关系</div>
        <el-table
          :data="relationList"
          border
          style="width: 100%"
          size="small"
          :header-cell-style="{background: '#fff', fontWeight: 'bold', borderBottom: '1px solid #eee'}"
          :cell-style="{borderBottom: '1px solid #eee'}"
        >
          <el-table-column prop="name" label="名词" />
          <el-table-column label="名词类型">
            <template #default="{row}">
              <el-tag :type="row.type === '指标集合' ? 'info' : 'success'" :class="['tag', row.type === '指标集合' ? 'set' : 'unit']">
                {{ row.type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="relation" label="关系" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import {ref} from 'vue';
import {Close} from '@element-plus/icons-vue';

const visible = ref(false);

const relationList = [
  {name: '安全监测', type: '指标集合', relation: '上一级'},
  {name: '环境监测', type: '指标集合', relation: '同级'},
  {name: '设备运行工况', type: '指标集合', relation: '同级'},
  {name: '作业时长', type: '指标', relation: '下一级'},
  {name: '入井次数', type: '指标', relation: '下一级'},
  {name: '作业人数', type: '指标', relation: '下一级'},
];

const openDialog = () => {
  visible.value = true;
};

const close = () => {
  visible.value = false;
};

// 暴露方法
defineExpose({openDialog});
</script>

<style lang="scss" scoped>
.safe-dialog {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    font-size: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    .close-icon {
      cursor: pointer;
      font-size: 18px;
    }
  }

  .dialog-body {
    font-size: 14px;
    line-height: 1.6;

    .section {
      margin-bottom: 15px;

      .title {
        font-weight: bold;
        margin-bottom: 5px;
      }

      .content {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;

        .tag {
          margin-left: 10px;
        }

        .set {
          background-color: #aee1f8;
          color: #000;
          border: none;
        }

        .unit {
          background-color: #b6f57b;
          color: #000;
          border: none;
        }
      }
    }

    .el-table {
      margin-top: 5px;

      .el-tag {
        font-size: 12px;
      }
    }
  }
}
</style>
