/**
 * 可视化主题相关 API
 */

import request from '@/config/axios';

// 主题数据接口定义
export interface ThemeData {
  id?: string;
  name: string;
  description?: string;
  createTime?: string;
  updateTime?: string;
  chartConfigs: {
    barConfig: any;
    barHorizontalConfig: any;
    lineConfig: any;
    pieConfig: any;
    funnelConfig: any;
    radarConfig: any;
    gaugeConfig: any;
  };
  pageConfig: {
    basicConfig: any;
    globalStyle: any;
    pageLayout: any;
  };
}

// 主题列表项接口
export interface ThemeListItem {
  id: string;
  name: string;
  description?: string;
  createTime: string;
  updateTime: string;
  thumbnail?: string; // 主题缩略图
  isDefault?: boolean; // 是否为默认主题
  askNumberFlag?: boolean; // 是否设置为问数样式
}

// API 响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 保存主题配置
 * @param themeData 主题数据
 * @returns Promise<ApiResponse<{id: string}>>
 */
export const saveTheme = async (themeData: ThemeData): Promise<ApiResponse<{id: string}>> => {
  console.log('保存主题数据:', themeData);

  // 构建请求参数，根据接口文档格式
  const requestData = {
    askNumberFlag: true,
    biConfigJson: themeData.pageConfig,
    coverImage: "",
    id: themeData.id || "",
    themeName: themeData.name,
    themeStyleJson: themeData.chartConfigs
  };

  try {
    const result = await request.post({
      url: 'https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/visualization/theme/config/create',
      data: requestData,
      headers: {
        'Content-Type': 'application/json',
      }
    });

    return {
      code: 200,
      message: '主题保存成功',
      data: {
        id: result.data || 'created_theme_id'
      }
    };
  } catch (error: any) {
    console.error('保存主题失败:', error);
    return {
      code: error.code || 500,
      message: error.message || '保存失败',
      data: null as any
    };
  }
};

/**
 * 更新主题配置
 * @param id 主题ID
 * @param themeData 主题数据
 * @returns Promise<ApiResponse>
 */
export const updateTheme = async (id: string, themeData: Partial<ThemeData>): Promise<ApiResponse> => {
  console.log('更新主题数据:', { id, themeData });

  // 构建请求参数，根据接口文档格式
  const requestData = {
    askNumberFlag: true,
    biConfigJson: themeData.pageConfig,
    coverImage: "",
    id: id,
    themeName: themeData.name,
    themeStyleJson: themeData.chartConfigs
  };

  try {
    await request.post({
      url: `https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/visualization/theme/config/update`,
      data: requestData,
      headers: {
        'Content-Type': 'application/json',
      }
    });

    return {
      code: 200,
      message: '主题更新成功',
      data: null
    };
  } catch (error: any) {
    console.error('更新主题失败:', error);
    return {
      code: error.code || 500,
      message: error.message || '更新失败',
      data: null
    };
  }
};

/**
 * 获取主题列表
 * @param params 查询参数
 * @returns Promise<ApiResponse<ThemeListItem[]>>
 */
export const getThemeList = async (params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
}): Promise<ApiResponse<{
  list: ThemeListItem[];
  total: number;
}>> => {
  console.log('获取主题列表:', params);

  try {
    const result = await request.post({
      url: 'https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/visualization/theme/config/list',
      params: params || {}
    });
    console.log('获取主题列表结果:', result);
    // 转换后端数据格式为前端需要的格式
    const list: ThemeListItem[] = (result || []).map((item: any) => ({
      id: item.id,
      name: item.themeName,
      description: item.description,
      createTime: item.createTime,
      updateTime: item.updateTime,
      thumbnail: item.coverImage,
      isDefault: item.isDefault || false,
      askNumberFlag: item.askNumberFlag || false
    }));

    return {
      code: 200,
      message: '获取成功',
      data: {
        list,
        total: result.data?.total || list.length
      }
    };
  } catch (error: any) {
    console.error('获取主题列表失败:', error);
    return {
      code: error.code || 500,
      message: error.message || '获取主题列表失败',
      data: {
        list: [],
        total: 0
      }
    };
  }
};

/**
 * 获取主题详情
 * @param id 主题ID
 * @returns Promise<ApiResponse<ThemeData>>
 */
export const getThemeDetail = async (id: string): Promise<ApiResponse<ThemeData>> => {
  console.log('获取主题详情:', id);

  try {
    const result = await request.get({
      url: `https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/visualization/theme/config/getById/${id}`
    });

    const item = result;

    // 获取图表配置和页面配置，提供默认值
    const chartConfigs = JSON.parse(item.themeStyleJson) || {
      barConfig: {},
      barHorizontalConfig: {},
      lineConfig: {},
      pieConfig: {},
      funnelConfig: {},
      radarConfig: {},
      gaugeConfig: {}
    };

    const pageConfig = JSON.parse(item.biConfigJson) || {
      basicConfig: {},
      globalStyle: {},
      pageLayout: {}
    };

    // 转换后端数据格式为前端需要的格式
    const themeData: ThemeData = {
      id: item.id,
      name: item.themeName,
      description: item.description,
      createTime: item.createTime,
      updateTime: item.updateTime,
      chartConfigs: chartConfigs,
      pageConfig: pageConfig
    };

    return {
      code: 200,
      message: '获取成功',
      data: themeData
    };
  } catch (error: any) {
    console.error('获取主题详情失败:', error);
    return {
      code: error.code || 404,
      message: error.message || '主题不存在',
      data: null as any
    };
  }
};

/**
 * 删除主题
 * @param id 主题ID
 * @returns Promise<ApiResponse>
 */
export const deleteTheme = async (id: string): Promise<ApiResponse> => {
  console.log('删除主题:', id);

  try {
    await request.delete({
      url: `https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/visualization/theme/config/deleteById/${id}`
    });

    return {
      code: 200,
      message: '删除成功',
      data: null
    };
  } catch (error: any) {
    console.error('删除主题失败:', error);
    return {
      code: error.code || 500,
      message: error.message || '删除失败',
      data: null
    };
  }
};

/**
 * 设置或取消问数样式
 * @param id 主题ID
 * @param flag 是否设置为问数样式，true为设置，false为取消
 * @returns Promise<ApiResponse>
 */
export const setAsAskNumberStyle = async (id: string, flag: boolean = true): Promise<ApiResponse> => {
  console.log(`${flag ? '设置' : '取消'}问数样式:`, id);

  try {
    await request.post({
      url: `https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/visualization/theme/config/updateAskNumberFlag`,
      data: {
        id: id,
        askNumberFlag: flag
      },
      headers: {
        'Content-Type': 'application/json',
      }
    });

    return {
      code: 200,
      message: flag ? '设置成功' : '取消成功',
      data: null
    };
  } catch (error: any) {
    console.error(`${flag ? '设置' : '取消'}问数样式失败:`, error);
    return {
      code: error.code || 500,
      message: error.message || (flag ? '设置失败' : '取消失败'),
      data: null
    };
  }
};

/**
 * 上传图片文件
 * @param file 图片文件
 * @returns Promise<ApiResponse<{fileName: string}>>
 */
export const uploadImage = async (file: File): Promise<ApiResponse<{fileName: string}>> => {
  console.log('上传图片文件:', file.name);

  try {
    const formData = new FormData();
    formData.append('file', file);

    const result = await request.upload({
      url: 'https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/upload',
      data: formData
    });

    console.log('上传图片结果:', result);

    // 从响应中获取文件名
    let fileName = result?.result;

    // 如果没有获取到文件名，使用原始文件名
    if (!fileName) {
      fileName = file.name;
      console.warn('未从响应中获取到文件名，使用原始文件名:', fileName);
    }

    return {
      code: 200,
      message: '上传成功',
      data: {
        fileName: fileName
      }
    };
  } catch (error: any) {
    console.error('上传图片失败:', error);
    return {
      code: error.code || 500,
      message: error.message || '上传失败',
      data: null as any
    };
  }
};

/**
 * 获取图片下载地址
 * @param fileName 文件名
 * @returns string 图片访问地址
 */
export const getImageUrl = (fileName: string): string => {
  if (!fileName) {
    console.warn('getImageUrl: fileName 为空');
    return '';
  }

  // 如果已经是完整的URL，直接返回
  if (fileName.startsWith('http://') || fileName.startsWith('https://')) {
    console.log('getImageUrl: 已是完整URL:', fileName);
    return fileName;
  }

  // 否则构建完整的URL
  const fullUrl = `https://374hfx9nwz6g.ngrok.xiaomiqiu123.top/basic/base/view/upload?fileName=${encodeURIComponent(fileName)}`;
  console.log('getImageUrl: 构建的完整URL:', fullUrl);
  return fullUrl;
};

/**
 * 测试图片URL是否可以访问
 * @param url 图片URL
 * @returns Promise<boolean>
 */
export const testImageUrl = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      console.log('图片加载成功:', url);
      resolve(true);
    };
    img.onerror = () => {
      console.error('图片加载失败:', url);
      resolve(false);
    };
    img.src = url;
  });
};
