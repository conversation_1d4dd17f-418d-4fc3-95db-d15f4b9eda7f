<template>
  <PageHeader title="知识库">
    <template #default>
      <div class="content-header">
        <el-input v-model="searchText" placeholder="搜索" class="search-input" :prefix-icon="Search" clearable @keyup.enter="getTableData" />
        <div>
          <el-button class="create-btn" @click="startTest">命中测试</el-button>
          <el-button type="primary" class="create-btn" @click="createKnowledgeBase">创建知识库</el-button>
        </div>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <el-table :data="knowledgeLibraries" style="width: 100%" max-height="630px" header-cell-class-name="header-cell" @row-dblclick="handleRowDblClick">
        <el-table-column prop="name" label="知识库名称">
          <template #default="scope">
            <div>{{ scope.row.name }}</div>
            <div :title="scope.row.description" class="table-description">{{ scope.row.description }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="fileCount" label="文件数量" width="80" align="center" />
        <el-table-column prop="knowledgeBaseId" label="知识库 ID" />
        <el-table-column prop="permission" label="权限" />
        <el-table-column prop="creator" label="创建人" width="130" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="updater" label="修改人" width="130" />
        <el-table-column prop="updateTime" label="修改时间" />
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <div class="operation">
              <el-button link type="primary" @click="editLibrary(scope.row)">编辑</el-button>
              <el-button link type="danger" @click="deleteLibrary(scope.row.id)">删除</el-button>
              <el-button link type="primary" @click="managePermissions(scope.row.id)">权限</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="content-footer">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </div>
  </el-card>
  <Permission ref="permissionRef" />
  <KnowEdit ref="knowEditRef" />
</template>

<script lang="ts" setup>
import {ref, reactive, onMounted, computed} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {Search} from '@element-plus/icons-vue';
import {useRouter} from 'vue-router';
import * as api from '@/api/know/knowPage/knowBase/index';
import PageHeader from '@/components/PageHeader.vue';
import {useKnowStore} from '@/store/modules/know';
import Permission from './components/Permission.vue';
import KnowEdit from './components/KnowEdit.vue';

const knowStore = useKnowStore();
const router = useRouter();

const knowEditRef = ref();
const permissionRef = ref();
const searchText = ref('');

// 知识库数据
const knowledgeLibraries = ref<any[]>([]);

// 分页相关
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: computed(() => knowledgeLibraries.value?.length ?? 0),
});
const resetPagination = () => {
  pagination.currentPage = 1;
  pagination.pageSize = 10;
};

// 获取表格数据
const getTableData = async () => {
  try {
    const res = await api.knowledgePage({
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      keyword: searchText.value,
    });
    knowledgeLibraries.value = res?.list;
  } catch (error) {
    console.log('error:', error);
  }
};

// 分页事件处理
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getTableData();
};
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  getTableData();
};

onMounted(() => {
  knowStore.clearDocumentId();
  getTableData();
});

const startTest = () => {
  router.push({
    name: 'HitTesting',
  });
};

const createKnowledgeBase = () => {
  router.push({name: 'CreateKnowBase'});
  knowStore.setKnowParamsForm({
    type: 'add',
  });
};

// 行双击事件
const handleRowDblClick = (row: any, column: any) => {
  if (column.label !== '知识库名称') return;
  router.push({name: 'KnowDetail'});
  knowStore.setKnowParamsForm(row);
};

// 编辑知识库
const editLibrary = (row: any) => {
  knowEditRef.value.open(row);
  // router.push({name: 'CreateKnowBase'});
  // knowStore.setKnowParamsForm({
  //   ...row,
  //   type: 'edit',
  // });
};

// 删除知识库
const deleteLibrary = async (id: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该知识库吗？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    await api.knowledgeDelete([id]);
    ElMessage.success('删除成功');
    resetPagination();
    getTableData(); // 刷新表格数据
  } catch (error) {
    console.log('error:', error);
  }
};

// 管理权限
const managePermissions = (id: any) => {
  permissionRef.value.openDialog();
};
</script>

<style lang="scss" scoped>
.create-btn {
  border-radius: 4px;
}
.content {
  .header-cell {
    background-color: #f5f7fa;
    font-weight: bold;
  }
  .operation {
    display: flex;
    justify-content: center;
    gap: 10px;
  }
  :deep(.el-table__row) {
    td {
      padding: 12px 0;
    }
  }
  .table-description {
    color: #bfbfbf;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
