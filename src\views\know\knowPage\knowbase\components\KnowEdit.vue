<template>
  <el-dialog v-model="dialogVisible" title="编辑知识库" width="500px" :before-close="handleClose">
    <!-- 表单区域 -->
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <!-- 知识库名称 -->
      <el-form-item label="知识库名称" prop="name">
        <el-input v-model="form.name" placeholder="支持中文、英文、数字、下划线、中划线、英文点（1~50字符）" max-length="50" show-word-limit />
      </el-form-item>

      <!-- 知识库备注 -->
      <el-form-item label="知识库备注" prop="description">
        <el-input type="textarea" v-model="form.description" :maxlength="400" show-word-limit placeholder="请输入知识库备注文案" />
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, reactive} from 'vue';
import {ElMessage} from 'element-plus';
import * as api from '@/api/know/knowPage/knowBase/index';

// 弹窗显隐控制
const dialogVisible = ref(false);

// 表单数据
const formRef = ref();
const form = reactive({
  id: '',
  knowledgeBaseId: '',
  name: '', // 知识库名称
  description: '', // 知识库备注
});

// 表单校验规则
const rules = {
  name: [{required: true, message: '请输入知识库名称', trigger: 'blur'}],
};

// 表单引用（用于校验）

// 关闭弹窗（取消/关闭按钮）
function handleClose() {
  dialogVisible.value = false;
}

// 确定按钮（校验表单）
function handleConfirm() {
  formRef.value?.validate(async valid => {
    if (valid) {
      try {
        await api.knowledgeUpdate(form);
        ElMessage.success('提交成功');
      } catch (error) {
        console.log('error:', error);
      }

      dialogVisible.value = false;
    }
  });
}

// 暴露给父组件的方法：打开弹窗（支持传入初始数据）
function open(initData?: Partial<typeof form>) {
  formRef.value?.resetFields();
  Object.keys(form).forEach(key => {
    form[key] = initData?.[key] || '';
  });
  dialogVisible.value = true;
}

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 8px;

  :deep .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #ebeef5;
  }
}

.el-form-item {
  margin-bottom: 16px;

  :deep .el-input__textarea {
    height: 80px; // 调整文本域高度
  }
}
</style>
