<template>
    <div class="svg-icon" :class="className" :style="style">
      <svg 
        v-bind="$attrs" 
        aria-hidden="true" 
        :width="sizeValue" 
        :height="sizeValue"
        :style="{ 'max-width': '100%', 'max-height': '100%' }"
      >
        <use :href="iconUrl" />
      </svg>
    </div>
  </template>
  
  <script setup>
  import { computed } from 'vue';
  
  const props = defineProps({
    name: {
      default:'menu/empty'
    },
    size: {
      type: [Number, String],
      default: 16
    },
    color: {
      type: String,
      default: 'currentColor'
    }
  });
  
  const iconUrl = computed(() => {
    return `/src/assets/svgs/${props.name}.svg`;
  });
  
  const className = computed(() => {
    return `svg-icon-${props.name}`;
  });
  
  const sizeValue = computed(() => {
    return typeof props.size === 'number' ? `${props.size}px` : props.size;
  });
  
  const style = computed(() => {
    return {
      color: props.color,
      display: 'inline-block',
      'vertical-align': 'middle'
    };
  });
  </script>
  
  <style scoped>
  .svg-icon {
    display: inline-block;
    vertical-align: middle;
  }
  
  .svg-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
    transition: all 0.3s;
  }
  </style>
      