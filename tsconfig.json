{
  "compilerOptions": {
    "strict": false,
    "noImplicitAny": false, // 允许隐式 any 类型（对象未声明类型时默认是 any，而非 {}）
    "strictPropertyInitialization": false, // 关闭属性初始化检查
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
