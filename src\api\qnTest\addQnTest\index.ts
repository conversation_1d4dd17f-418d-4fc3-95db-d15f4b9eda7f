import request from '@/config/axios'

// 测试
export const askNumberTest = async (params: any) => {
  return await request.get({ url: '/datamanage/ask-number/test', params })
}

// 创建测试问题
export const askNumberCreate = async (data: any) => {
  return await request.post({
    url: `/datamanage/ask-number/create`,
    data,
    headers: { 'Content-Type': 'application/json' }
  })
}
// 查看
export const askNumberGet = (params: any) => {
  return request.get({ url: '/datamanage/ask-number/get', params })
}
// 更新问数测试
export const askNumberUpdate = (data: any) => {
  return request.put({ url: `/datamanage/ask-number/update`, data })
}
// 获取批量测试数据
export const askNumberBatchTest = async (data: any) => {
  return await request.post({
    url: `/datamanage/ask-number/batchTest`,
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}
// 批量测试传入全部id预加载接口
export const askNumberBatchTestQuery = async (data: any) => {
  return await request.post({
    url: `/datamanage/ask-number/batchTestQuery`,
    data,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
