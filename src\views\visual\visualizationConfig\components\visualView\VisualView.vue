<template>
  <div
    class="visual-view-card"
    @click="handleClick"
    :style="cardStyle"
  >
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from "vue";
import * as echarts from "echarts";
import { useVisualStore } from "@/store/modules/visual";

const visualStore = useVisualStore();
const chartRef = ref(null);
let chartInstance = null;
let resizeObserver = null;

const props = defineProps({
  chartOption: Object,
  chartType: String
});

// 定义emit
const emit = defineEmits(["click"]);

// 处理点击事件
const handleClick = () => {
  emit("click");
};

// 计算卡片样式 - 只应用当前图表类型的样式
const cardStyle = computed(() => {
  // 使用 props.chartType 而不是 visualStore.chartType，确保每个图表独立
  const currentChartType = props.chartType || visualStore.chartType;
  const config = visualStore.chartConfig[currentChartType];
  const globalStyle = visualStore.pageConfig.globalStyle;

  // 从 cardStyle 中获取边框配置，而不是从 grid
  const cardStyleConfig = config.cardStyle || {};
  const borderWidth = cardStyleConfig.borderWidth || 1;
  const borderColor = cardStyleConfig.borderColor || '#000000';
  const borderStyle = cardStyleConfig.borderStyle || 'solid';
  const borderRadius = cardStyleConfig.borderRadius || 0;

  // 处理背景色：根据 colorMode 决定使用 backgroundColor 还是 background
  const backgroundStyle = {};
  console.log(`Card style - Chart: ${currentChartType}, colorMode: ${config.colorMode}, backgroundColor: ${config.backgroundColor}`);

  if (config.colorMode === 'gradient') {
    // 渐变模式：使用 background 属性
    backgroundStyle.background = config.backgroundColor || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    console.log('Card using gradient background:', backgroundStyle.background);
  } else {
    // 纯色模式：使用 backgroundColor 属性
    backgroundStyle.backgroundColor = config.backgroundColor || '#ffffff';
    console.log('Card using solid background:', backgroundStyle.backgroundColor);
  }

  return {
    marginRight: globalStyle.componentSpacing,
    marginBottom: globalStyle.componentSpacing,
    borderRadius: `${borderRadius}px`,
    border: `${borderWidth}px ${borderStyle} ${borderColor}`,
    overflow: 'hidden', // 确保圆角效果
    ...backgroundStyle, // 展开背景样式
  };
});

// 将CSS渐变字符串转换为ECharts渐变对象
const parseGradientToECharts = (gradientStr) => {
  console.log('=== Parsing gradient ===');
  console.log('Input:', gradientStr);

  if (!gradientStr || typeof gradientStr !== 'string') {
    console.log('Invalid gradient string, returning transparent');
    return 'transparent';
  }

  // 如果不是渐变字符串，直接返回
  if (!gradientStr.includes('gradient')) {
    console.log('Not a gradient string, returning as is');
    return gradientStr;
  }

  try {
    // 直接使用正则表达式提取所有颜色值
    const colorRegex = /#[0-9a-fA-F]{3,6}|rgb\([^)]+\)|rgba\([^)]+\)|hsl\([^)]+\)|hsla\([^)]+\)/g;
    const colorMatches = gradientStr.match(colorRegex);

    console.log('Color matches found:', colorMatches);

    if (colorMatches && colorMatches.length >= 2) {
      // 解析方向
      let x = 0, y = 0, x2 = 1, y2 = 1; // 默认对角线

      if (gradientStr.includes('to right')) {
        x = 0; y = 0; x2 = 1; y2 = 0; // 从左到右
        console.log('Direction: to right');
      } else if (gradientStr.includes('to left')) {
        x = 1; y = 0; x2 = 0; y2 = 0; // 从右到左
        console.log('Direction: to left');
      } else if (gradientStr.includes('to bottom')) {
        x = 0; y = 0; x2 = 0; y2 = 1; // 从上到下
        console.log('Direction: to bottom');
      } else if (gradientStr.includes('to top')) {
        x = 0; y = 1; x2 = 0; y2 = 0; // 从下到上
        console.log('Direction: to top');
      } else if (gradientStr.includes('0deg')) {
        x = 0; y = 1; x2 = 0; y2 = 0; // 0度 = 从下到上
        console.log('Direction: 0deg');
      } else if (gradientStr.includes('90deg')) {
        x = 0; y = 0; x2 = 1; y2 = 0; // 90度 = 从左到右
        console.log('Direction: 90deg');
      } else if (gradientStr.includes('180deg')) {
        x = 0; y = 0; x2 = 0; y2 = 1; // 180度 = 从上到下
        console.log('Direction: 180deg');
      } else if (gradientStr.includes('270deg')) {
        x = 1; y = 0; x2 = 0; y2 = 0; // 270度 = 从右到左
        console.log('Direction: 270deg');
      } else {
        console.log('Direction: default diagonal');
      }

      // 构建颜色停止点
      const colorStops = colorMatches.map((color, index) => ({
        offset: index / (colorMatches.length - 1),
        color: color.trim()
      }));

      const result = {
        type: 'linear',
        x: x,
        y: y,
        x2: x2,
        y2: y2,
        colorStops: colorStops
      };

      console.log('=== Generated ECharts gradient ===');
      console.log('Coordinates:', { x, y, x2, y2 });
      console.log('Color stops:', colorStops);
      console.log('Full result:', result);

      return result;
    } else {
      console.warn('Not enough colors found in gradient string');
    }
  } catch (error) {
    console.error('Error parsing gradient:', error);
  }

  // 如果解析失败，返回简单的默认渐变
  const defaultGradient = {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 1,
    colorStops: [
      { offset: 0, color: '#667eea' },
      { offset: 1, color: '#764ba2' }
    ]
  };

  console.log('Using default gradient:', defaultGradient);
  return defaultGradient;
};

// 处理图表配置，确保背景图片正确显示
const getProcessedChartOption = () => {
  const option = { ...props.chartOption };

  // 处理背景图片
  if (option.graphic && option.graphic.style && option.graphic.style.image) {
    option.graphic = {
      ...option.graphic,
      style: {
        ...option.graphic.style,
        width: chartRef.value?.offsetWidth || '100%',
        height: chartRef.value?.offsetHeight || '100%',
      }
    };
  }

  // 处理背景色
  const currentChartType = props.chartType || visualStore.chartType;
  const config = visualStore.chartConfig[currentChartType];

  if (config.colorMode === 'gradient') {
    // 渐变模式：将CSS渐变字符串转换为ECharts渐变对象
    option.backgroundColor = parseGradientToECharts(config.backgroundColor);
  } else {
    // 纯色模式
    option.backgroundColor = config.backgroundColor || 'transparent';
  }

  return option;
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    updateChart();
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    const option = getProcessedChartOption();
    chartInstance.setOption(option, true); // true 表示不合并，完全替换
  }
};

// 监听窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
    // 重新设置背景图片尺寸
    setTimeout(() => {
      updateChart();
    }, 100);
  }
};

// 初始化容器大小监听
const initResizeObserver = () => {
  if (chartRef.value && window.ResizeObserver) {
    let lastWidth = 0;
    let lastHeight = 0;

    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;

        // 只有当尺寸真正发生变化时才触发resize
        if (Math.abs(width - lastWidth) > 1 || Math.abs(height - lastHeight) > 1) {
          lastWidth = width;
          lastHeight = height;

          // 使用防抖，避免频繁触发
          clearTimeout(resizeObserver.timer);
          resizeObserver.timer = setTimeout(() => {
            if (chartInstance) {
              chartInstance.resize();
            }
          }, 150);
        }
      }
    });

    resizeObserver.observe(chartRef.value);
  }
};

// 清理容器大小监听
const cleanupResizeObserver = () => {
  if (resizeObserver) {
    if (resizeObserver.timer) {
      clearTimeout(resizeObserver.timer);
    }
    resizeObserver.disconnect();
    resizeObserver = null;
  }
};

// 监听配置变化
watch(() => props.chartOption, () => {
  updateChart();
}, { deep: true });

// 监听当前图表类型的配置变化
watch(() => {
  const currentChartType = props.chartType || visualStore.chartType;
  return visualStore.chartConfig[currentChartType];
}, () => {
  updateChart();
}, { deep: true });

onMounted(async () => {
  await nextTick();
  initChart();
  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
  // 监听容器大小变化
  initResizeObserver();
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener("resize", handleResize);
  // 清理容器大小监听
  cleanupResizeObserver();
});

// 暴露方法供父组件调用
defineExpose({
  updateChart,
});
</script>

<style scoped lang="scss">
.visual-view-card {
  position: relative;
  display: block; // 改为block，让容器能够自适应父容器
  z-index: 2;
  width: 100%; // 宽度自适应父容器
  height: 300px; // 固定高度，避免无限增长
  background-color: #fff;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  padding: 8px; // 减少内边距，让图表与卡片间隙更小
  transition: all 0.3s ease;
  box-sizing: border-box; // 确保padding不会影响总尺寸

  &:hover {
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.15);
  }

  .chart-container {
    width: 100%;
    height: 100%;
    position: relative;

    // 确保 ECharts 容器继承父容器的圆角
    > div {
      border-radius: inherit;
      overflow: hidden;
    }
  }
}
</style>
