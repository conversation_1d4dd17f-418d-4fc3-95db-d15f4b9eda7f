<template>
  <el-dialog v-model="dialogVisible" title="" width="50%" :before-close="handleClose">
    <template #header>
      <!-- 顶部统计信息 -->
      <div class="dialog-header">
        <span class="dialog-header-title">添加知识库</span>
        <el-tooltip content="<span>The content can be <strong>HTML</strong></span>" raw-content>
          <el-icon class="dialog-header-icon"><Warning /></el-icon>
        </el-tooltip>
        <span class="dialog-header-info">已添加{{ addedCount }}个 / 共计可添加{{ maxCount }}个</span>
      </div>
    </template>

    <!-- 搜索表单和创建按钮 -->
    <div class="search-bar">
      <el-input v-model="searchKeyword" placeholder="搜索知识库关键词" clearable @clear="handleSearch" @input="handleSearch" :suffix-icon="Search" class="search-input" />
      <el-button @click="handleSearch">
        <el-icon><Refresh /></el-icon>
      </el-button>
      <el-button type="primary" @click="handleCreate">创建知识库</el-button>
    </div>

    <!-- 知识库列表 -->
    <div class="kb-list">
      <div v-for="(kb, index) in kbList" :key="index" class="kb-item">
        <!-- 图标 -->
        <div class="kb-icon">
          <el-icon color="#67C23A" class="doc-icon"><Document /></el-icon>
        </div>

        <!-- 信息区域 -->
        <div class="kb-info">
          <div class="kb-name">{{ kb.name }}</div>
          <div class="kb-desc">{{ kb.description }}</div>
          <div class="kb-meta">{{ kb.fileCount }}个</div>
        </div>

        <!-- 操作按钮 -->
        <div class="kb-actions">
          <el-button v-if="addedIds.includes(kb.knowledgeBaseId)" type="danger" plain @click="handleRemove(kb)">移除</el-button>
          <el-button v-else @click="handleAdd(kb)">添加</el-button>
          <el-button @click="handleNewFile(kb)">新增文件</el-button>
          <el-button @click="handleView(kb)">查看</el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination background layout="prev, pager, next" :total="pagination.total" :page-size="pagination.pageSize" @current-change="handlePageChange" :current-page="pagination.currentPage" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, computed, reactive, onMounted} from 'vue';
import {Document, Warning, Search, Refresh} from '@element-plus/icons-vue';
import {useKnowStore} from '@/store/modules/know';
import {useRouter} from 'vue-router';
import * as knowApi from '@/api/know/knowPage/knowBase/index';

const knowStore = useKnowStore();
const router = useRouter();
const emit = defineEmits(['handleClose']);

// 控制弹窗显示
const dialogVisible = ref(false);
// 已添加数量
const addedCount = ref(2);
// 最大可添加数量
const maxCount = ref(50);
// 搜索框绑定值
const searchKeyword = ref('');

// 统一管理分页配置
const pagination = reactive({
  currentPage: 1, // 当前页码
  pageSize: 4, // 每页显示数量
  total: 0, // 总条数
});

// 知识库列表
const kbList = ref([
  {
    id: 1,
    name: '知识库名称',
    description: '知识库描述',
    fileCount: 2,
    added: true,
    knowledgeBaseId: '707b9461-1b75-48c4-8643-694278e122a4',
  },
]);

// 已添加知识库id数组
const addedIds = ref<any[]>([]);

onMounted(() => {
  getTableData();
});

const getTableData = async () => {
  try {
    const res = await knowApi.knowledgePage({
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      keyword: searchKeyword.value,
    });
    kbList.value = res?.list || [];
    pagination.total = res?.total || 0;
  } catch (error) {
    console.log('error:', error);
  }
};

// 暴露给父组件的打开方法
const open = () => {
  dialogVisible.value = true;
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
};

// 搜索栏筛选逻辑
const handleSearch = () => {
  pagination.currentPage = 1; // 搜索时重置页码
  getTableData();
};

// 创建知识库
const handleCreate = () => {
  console.log('点击创建知识库');
  // router.push({ name: 'CreateKnowBase' });
  const route = router.resolve({name: 'CreateKnowBase'});
  window.open(route.href, '_blank');
  // 新建知识库不须要带参数了
  knowStore.setKnowParamsForm({
    type: 'add',
  });
};

// 添加知识库
const handleAdd = (kb: any) => {
  if (addedCount.value < maxCount.value && !addedIds.value.includes(kb.knowledgeBaseId)) {
    addedIds.value.push(kb.knowledgeBaseId);
    addedCount.value++;
    console.log('添加知识库:', kb, addedIds.value);
  } else {
    console.log('已达到最大添加数量或已添加');
  }
};

// 移除知识库
const handleRemove = (kb: any) => {
  const idx = addedIds.value.indexOf(kb.knowledgeBaseId);
  if (idx !== -1) {
    addedIds.value.splice(idx, 1);
    addedCount.value--;
    console.log('移除知识库:', kb, addedIds.value);
  }
};

// 新增文件
const handleNewFile = (kb: any) => {
  console.log('新增文件:', kb);
  const route = router.resolve({name: 'CreateKnowBase'});
  window.open(route.href, '_blank');
  knowStore.setKnowParamsForm({
    ...knowStore.knowParamsForm,
    type: 'addFeedback',
  });
};

// 查看知识库详情
const handleView = (kb: any) => {
  // console.log('查看知识库:', kb);
  // dialogVisible.value = false; // 关闭当前弹窗
  // emit('handleClose'); // 新增事件触发
  router.push({name: 'KnowDetail'});
  knowStore.setKnowParamsForm(kb);
};

// 分页切换
const handlePageChange = (page: number) => {
  pagination.currentPage = page;
  getTableData();
};

// 暴露方法给父组件
defineExpose({
  open,
  kbList,
  addedIds,
});
</script>

<style scoped lang="scss">
.dialog-header {
  display: flex;
  align-items: center;
  .dialog-header-title {
    font-size: 18px;
    font-weight: 400;
  }
  .dialog-header-icon {
    margin-left: 5px;
    font-size: 16px;
    color: #999;
  }
  .dialog-header-info {
    display: inline-block;
    padding: 3px 5px;
    font-size: 14px;
    background: #f5f7fa;
    color: #000;
    border-radius: 10px;
    margin-left: 20px;
  }
}

.search-bar {
  display: flex;
  align-items: center;
  justify-content: end;
  margin-bottom: 20px;

  .search-input {
    width: 200px;
    margin-right: 15px;
  }
}

.kb-list {
  margin-bottom: 20px;
}

.kb-item {
  display: flex;
  align-items: center;
  padding: 14px 0;
  border-bottom: 1px solid #ebeef5;
}

.kb-icon {
  display: flex;
  align-items: center;
  margin-right: 12px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #67c23a;
  justify-content: center;
}

.doc-icon {
  font-size: 20px;
  color: white !important;
}

.kb-info {
  flex: 1;
}

.kb-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.kb-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.kb-meta {
  font-size: 12px;
  color: #999;
}

.kb-actions {
  display: flex;
  gap: 16px;
}

.pagination {
  display: flex;
  justify-content: end;
  margin: auto;
  margin-top: 20px;
}
</style>
