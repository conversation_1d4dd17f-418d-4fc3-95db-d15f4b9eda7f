<template>
  <PageHeader title="切片详情">
    <template #page-header-back>
      <el-icon class="header-back-icon" @click="goBack"><ArrowLeftBold /></el-icon>
    </template>
    <template #default>
      <div class="content-header">
        <div></div>
        <div>
          <el-button type="primary" plain @click="startTest">命中测试</el-button>
        </div>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <div class="dialog-body">
        <!-- 左侧：原文对照 -->
        <div class="left-panel">
          <div class="panel-header">
            <span>原文对照</span>
          </div>
          <div class="panel-content">
            <el-scrollbar height="600px">
              <pre>{{ rawText }}</pre>
            </el-scrollbar>
          </div>
        </div>

        <!-- 中间：切片信息 -->
        <div class="middle-panel">
          <div class="panel-header">
            <span>切片信息（{{ originSlices.length }}）</span>
            <el-input :suffix-icon="Search" v-model="searchText" style="width: 25%" placeholder="请输入切片内容" @keyup.enter="fetchData"></el-input>
          </div>
          <div class="panel-content">
            <!-- 切片列表 -->
            <div v-for="(item, index) in originSlices" :key="item.id" class="slice-item" :class="{active: activeSliceIndex === item.id}" @click="handleSelectSlice(item.id)">
              <!-- 切片标题栏（模拟截图的蓝色标题） -->
              <div class="slice-title">自动切片{{ index + 1 }}</div>
              <!-- 切片内容区 -->
              <div class="slice-content">
                <pre>{{ item.content }}</pre>
              </div>
            </div>

            <!-- 分页 -->
            <div class="pagination">
              <el-pagination
                background
                layout="prev, pager, next, sizes"
                :page-sizes="[10, 20, 30]"
                v-model:page-size="pageSize"
                v-model:current-page="currentPage"
                :total="originSlices.length"
                @size-change="handlePagination"
                @current-change="handlePagination"
              />
            </div>
          </div>
        </div>

        <!-- 右侧：切片知识点 -->
        <div class="right-panel">
          <div class="panel-header">
            <span>切片知识点（{{ knowledgeSlices.length }}）</span>
            <el-button :icon="Plus" @click="handleCreateKnowledge">新建</el-button>
          </div>
          <div class="panel-content">
            <el-scrollbar height="600px">
              <div v-for="(item, index) in knowledgeSlices" :key="index" class="knowledge-item">
                <div class="knowledge-title">
                  {{ item.title }}
                </div>
                <div class="knowledge-content">
                  <span>{{ item.content }}</span>
                  <span>|</span>
                  <el-button link :icon="Edit" @click="handleEditKnowledge(item)" />
                  <el-button link :icon="Delete" @click="handleDeleteKnowledge(item)" />
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
  </el-card>
  <el-dialog v-model="sliceKnowDialogVisible" :title="sliceKnowDialogTitle" width="500" :before-close="sliceKnowHandleClose">
    <el-input type="textarea" v-model="sliceKnowValue" placeholder-class="" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="sliceKnowHandleClose">取消</el-button>
        <el-button type="primary" @click="sliceKnowSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {ref, onMounted} from 'vue';
import {ElMessageBox} from 'element-plus';
import {Plus, Search, Edit, Delete, ArrowLeftBold} from '@element-plus/icons-vue';
import * as api from '@/api/know/knowPage/sliceDetails/index';
import {useKnowStore} from '@/store/modules/know';
import PageHeader from '@/components/PageHeader.vue';
import {useRouter} from 'vue-router';

const router = useRouter();
const knowStore = useKnowStore();

const sliceKnowDialogVisible = ref(false);
const sliceKnowDialogTitle = ref('');
const documentId = ref<any>(knowStore.knowDocumentId);
const knowledgeBaseId = ref<any>(knowStore.knowParamsForm.knowledgeBaseId);
const searchText = ref('');
const sliceKnowValue = ref('');

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
// 选中的切片索引
const activeSliceIndex = ref(0);

// 原文内容
const rawText = `
    乌海能源集团调度室工作内容
    1、调度日报
    主要包括原煤生产、掘进进尺、入洗量、洗精煤量、发电情况、库存情况、收车及销售情况、外销、调运情况、安全情况、生产情况一览表、安全监测系统报警统计等。
    (1) 原煤生产
    主要有生产矿井当日（当月/当年）计划、实际、超欠原煤吨数；欠量原因。
    如全公司原煤产量完成 xx 吨，欠日计划 xx 吨，月累计划完成 xx 吨，欠月计划 xx 吨。
    前置原因：棋盘井煤矿主供水管路故障影响产量；露天煤矿搬家倒面，无产量。
    ...
  `;

// 切片信息（模拟截图内容）
const originSlices = ref<any[]>([]);

// 切片知识点
const knowledgeSlices = ref<any[]>([]);

onMounted(async () => {
  await fetchData();
});
const startTest = () => {
  router.push({name: 'HitTesting'});
};
const goBack = () => {
  router.back();
};
// 接口请求
const fetchData = async () => {
  try {
    const res = await api.chunkPage({
      documentId: documentId.value,
      knowledgeBaseId: knowledgeBaseId.value,
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchText.value,
    });
    originSlices.value = res.list || [];
    activeSliceIndex.value = res.list[0]?.id;
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};
// 选择切片信息
const handleSelectSlice = (id: number) => {
  activeSliceIndex.value = id;
};
// 分页处理
const handlePagination = () => {
  fetchData();
};

// 知识点编辑
const handleEditKnowledge = (item: any) => {
  sliceKnowDialogTitle.value = '编辑知识点';
  sliceKnowValue.value = item.title;
  sliceKnowDialogVisible.value = true;
};

// 知识点删除
const handleDeleteKnowledge = (item: any) => {
  ElMessageBox.confirm('删除后该知识点将不可用于检索召回，是否确定删除？', '确认要删除选中的切片知识点吗？', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {})
    .catch(() => {});
  // const index = knowledgeSlices.value.findIndex(i => i === item);
  // if (index > -1) knowledgeSlices.value.splice(index, 1);
};

// 知识点新建
const handleCreateKnowledge = () => {
  sliceKnowDialogVisible.value = true;
  sliceKnowDialogTitle.value = '新建知识点';
  // knowledgeSlices.value.unshift({
  //   title: '新增知识点标题',
  //   content: '新增知识点内容...',
  // });
};
// 关闭切片知识点弹窗
const sliceKnowHandleClose = () => {
  sliceKnowDialogVisible.value = false;
};
const sliceKnowSubmit = async () => {
  // try {
  //   if (sliceKnowDialogTitle.value === '新建知识点') {
  //     await api.createChunk({
  //       knowledgeBaseId: knowledgeBaseId.value,
  //       content: sliceKnowValue.value
  //     });
  //   } else {
  //     await api.updateChunk({
  //       id: activeSliceIndex.value,
  //       content: sliceKnowValue.value
  //     });
  //   }
  //   ElMessage.success('操作成功');
  //   await fetchData();
  // } catch (error) {
  //   ElMessage.error('操作失败');
  // } finally {
  //   sliceKnowHandleClose();
  //   sliceKnowValue.value = '';
  // }
};
</script>

<style lang="scss" scoped>
.dialog-body {
  display: flex;
  gap: 16px;
  height: 100%;

  .left-panel,
  .middle-panel,
  .right-panel {
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f9f9f9;
    padding: 12px;
  }

  // 左侧原文
  .left-panel {
    width: 40%;
    .panel-header {
      margin-bottom: 8px;
      font-weight: bold;
    }
    .panel-content {
      flex: 1;
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      white-space: pre-wrap;
      font-size: 13px;
      line-height: 1.6;
    }
  }

  // 中间切片信息
  .middle-panel {
    width: 40%;
    .panel-header {
      margin-bottom: 8px;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .panel-content {
      flex: 1;
      overflow-y: auto;
      background: #fff;
      padding: 10px;
      border-radius: 4px;

      .slice-item {
        margin-bottom: 12px;
        border: 1px solid #d9e1f1;
        border-radius: 6px;
        overflow: hidden;
        transition: all 0.2s ease;
        cursor: pointer;

        &.active {
          border-color: #409eff;
          background-color: #eef3fe;
          box-shadow: 0 0 8px rgba(64, 158, 255, 0.1);
        }

        // 切片标题（模拟截图蓝色背景）
        .slice-title {
          width: fit-content;
          border-radius: 8px 0 8px;
          background: #eaf4ff;
          color: #333;
          font-weight: bold;
          padding: 6px 10px;
          font-size: 14px;
          border-bottom: 1px solid #d9e1f1;
        }

        // 切片内容区
        .slice-content {
          padding: 10px;
          pre {
            margin: 0;
            white-space: pre-wrap;
            font-size: 13px;
            line-height: 1.6;
            color: #444;
          }
        }
      }
    }
    .pagination {
      display: flex;
      justify-content: end;
      margin-top: 12px;
    }
  }

  // 右侧切片知识点
  .right-panel {
    width: 20%;
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-weight: bold;
    }
    .panel-content {
      flex: 1;
      overflow-y: auto;
      background: #fff;
      padding: 10px;
      border-radius: 4px;

      .knowledge-item {
        border: 1px solid #ccc;
        padding: 10px;
        border-radius: 8px;
        background-color: #eef3fe;
        margin-bottom: 10px;

        .knowledge-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: bold;
          margin-bottom: 4px;
        }

        .knowledge-content {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          white-space: pre-wrap;
          font-size: 13px;
          color: #666;
          .el-button {
            color: #666;
          }
        }
      }
    }
  }
}

:deep(.el-pagination) {
  margin-top: 10px;
}
</style>
