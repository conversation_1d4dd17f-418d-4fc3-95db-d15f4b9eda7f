<template>
  <!-- 顶部工具栏 -->
  <el-row align="middle" style="margin-bottom: 10px">
    <el-button type="primary" @click="save">保存</el-button>
    <el-button type="danger" @click="deleteSelectedEdge" style="margin-left: 10px" :disabled="!selectedEdge">删除边</el-button>
    <el-button type="danger" @click="deleteSelectedNode" style="margin-left: 10px" :disabled="!selectedNode">删除节点</el-button>
    <el-button type="success" @click="addNode" style="margin-left: 10px">新增节点</el-button>
    <label style="margin-left: 20px">
      关系类型：
      <el-select v-model="relationType" placeholder="请选择关系类型" style="width: 200px">
        <el-option v-for="(color, type) in edgeColors" :key="type" :label="type" :value="type" />
      </el-select>
    </label>
    <el-button type="success" @click="details" style="margin-left: 10px">详情</el-button>
  </el-row>

  <div ref="container" class="x6-container"></div>

  <el-dialog v-model="editDialogVisible" title="编辑节点" width="300px">
    <el-input v-model="editNodeLabel" placeholder="请输入节点名称" />
    <template #footer>
      <el-button @click="editDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmEdit">确认</el-button>
    </template>
  </el-dialog>
  <relationshipDialog ref="relationshipDialogRef"/>
</template>

<script setup>
import {ref, onMounted, onBeforeUnmount, watch, nextTick} from 'vue';
import {ElMessage} from 'element-plus';
import {Graph} from '@antv/x6';
import dagre from 'dagre';
import * as api from '@/api/know/noun/index';
import relationshipDialog from './relationshipDialog.vue';

const relationshipDialogRef=ref(null)

const unifiedNodeColor = '#A0CFFF';
const edgeColors = {包含: '#5F95FF', 计算: '#19be6b', 关联: '#FCC932'};
const container = ref(null);
const relationType = ref('包含');
const selectedEdge = ref(null);
const selectedNode = ref(null);
const editDialogVisible = ref(false);
const editNodeLabel = ref('');
const editingNode = ref(null);
const graph = ref(null);

const getCircleSize = label => Math.max(100, label.length * 14 + 30);

const getNodeAttrs = (fill = unifiedNodeColor, isSelected = false, size = 100) => ({
  body: {
    fill,
    stroke: isSelected ? 'red' : fill,
    strokeWidth: 2,
  },
  label: {
    fill: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAnchor: 'middle',
    refX: '50%',
    refY: '50%',
    textWrap: {
      width: size - 20,
      height: size - 20,
      ellipsis: true,
    },
  },
});

const getEdgeAttrs = type => ({
  line: {
    stroke: edgeColors[type],
    strokeWidth: 2,
    targetMarker: {name: 'classic', size: 8, fill: edgeColors[type]},
  },
});

const getEdgeLabel = type => [
  {
    attrs: {
      label: {
        text: type,
        fill: edgeColors[type],
        fontSize: 12,
        fontWeight: 'bold',
        textAnchor: 'middle',
        refY: -10,
      },
    },
    position: 0.5,
  },
];

const getPorts = () => {
  const dirs = ['top', 'bottom', 'left', 'right'];
  return {
    groups: Object.fromEntries(
      dirs.map(dir => [
        dir,
        {
          position: dir,
          attrs: {
            circle: {r: 6, magnet: true, stroke: '#31d0c6', strokeWidth: 2, fill: '#fff'},
          },
        },
      ])
    ),
    items: dirs.map((dir, i) => ({id: `port${i + 1}`, group: dir})),
  };
};

const generateNodeId = () => `${Date.now()}`;

const layoutGraph = (nodes, edges) => {
  const g = new dagre.graphlib.Graph();
  g.setGraph({rankdir: 'LR', marginx: 20, marginy: 20});
  g.setDefaultEdgeLabel(() => ({}));
  nodes.forEach(({id}) => g.setNode(id, {width: 100, height: 100}));
  edges.forEach(({source, target}) => g.setEdge(source, target));
  dagre.layout(g);
  return nodes.map(node => {
    const pos = g.node(node.id);
    return {...node, x: pos.x - 50, y: pos.y - 50};
  });
};

function clearSelection() {
  if (selectedNode.value) {
    const size = selectedNode.value.getSize().width;
    selectedNode.value.setAttrs(getNodeAttrs(unifiedNodeColor, false, size));
    selectedNode.value = null;
  }
  if (selectedEdge.value) {
    const type = selectedEdge.value.getData()?.relationType || '包含';
    selectedEdge.value.setAttrs(getEdgeAttrs(type));
    selectedEdge.value = null;
  }
}

const selectEdge = edge => {
  if (selectedEdge.value !== edge) {
    clearSelection();
    selectedEdge.value = edge;
    edge.setAttrs({
      line: {stroke: 'red', targetMarker: {name: 'classic', size: 8, fill: 'red'}},
    });
  }
};

const selectNode = node => {
  if (selectedNode.value !== node) {
    clearSelection();
    const size = node.getSize().width;
    selectedNode.value = node;
    node.setAttrs(getNodeAttrs(unifiedNodeColor, true, size));
  }
};

const addNode = () => {
  if (!graph.value) return;
  const id = generateNodeId();
  const label = '新节点';
  const size = getCircleSize(label);
  graph.value.addNode({
    id,
    shape: 'circle',
    x: 0,
    y: 0,
    width: size,
    height: size,
    label,
    attrs: getNodeAttrs(unifiedNodeColor, false, size),
    ports: getPorts(),
    data: {fill: unifiedNodeColor},
  });
};

const deleteSelectedEdge = () => {
  if (!selectedEdge.value) return ElMessage.warning('请选择一条边');
  selectedEdge.value.remove();
  selectedEdge.value = null;
};

const deleteSelectedNode = () => {
  if (!selectedNode.value) return ElMessage.warning('请选择一个节点');
  selectedNode.value.remove();
  selectedNode.value = null;
};

// const save = async () => {
//   const nodes = graph.value.getNodes().map(node => ({ id: node.id, label: node.getLabel() }));
//   const edges = graph.value.getEdges().map(edge => ({
//     id: edge.id,
//     source: edge.getSourceCellId(),
//     target: edge.getTargetCellId(),
//     relationType: edge.getData()?.relationType,
//   }));
//   try {
//     await api.basicKnowledge({ nodeList: nodes, connectList: edges });
//     ElMessage.success('保存成功');
//   } catch (error) {
//     console.error('error:', error);
//     ElMessage.error('保存失败');
//   }
// };
const save = async () => {
  const nodes = graph.value.getNodes().map(node => ({
    id: node.id,
    label: node.getLabel(),
  }));

  const edges = graph.value.getEdges().map(edge => ({
    id: edge.id,
    source: edge.getSourceCellId(),
    target: edge.getTargetCellId(),
    relationType: edge.getData()?.relationType,
  }));

  try {
    await api.basicKnowledge({nodeList: nodes, connectList: edges});
    ElMessage.success('保存成功');

    // 重新拉取数据并重建图谱
    const res = await api.knowledgeList();

    // 正确清空图谱
    graph.value.getCells().forEach(cell => graph.value.removeCell(cell));

    const laidOut = layoutGraph(res.nodeList, res.connectList);
    laidOut.forEach(({id, x, y, label}) => {
      const size = getCircleSize(label);
      graph.value.addNode({
        id,
        shape: 'circle',
        x,
        y,
        width: size,
        height: size,
        label,
        attrs: getNodeAttrs(unifiedNodeColor, false, size),
        data: {fill: unifiedNodeColor},
        ports: getPorts(),
      });
    });

    res.connectList.forEach(({id, source, target, relationType}) => {
      graph.value.addEdge({
        id,
        source: {cell: source, port: 'port4'},
        target: {cell: target, port: 'port3'},
        attrs: getEdgeAttrs(relationType),
        labels: getEdgeLabel(relationType),
        data: {relationType},
      });
    });
  } catch (error) {
    console.error('error:', error);
    ElMessage.error('保存失败');
  }
};

const details = () => {
  relationshipDialogRef.value.openDialog()
};

const confirmEdit = () => {
  if (editingNode.value) {
    const label = editNodeLabel.value;
    const size = getCircleSize(label);
    editingNode.value.setSize(size, size);
    editingNode.value.setAttrs(getNodeAttrs(unifiedNodeColor, true, size));
    editingNode.value.setLabel(label);
  }
  editingNode.value = null;
  editDialogVisible.value = false;
};

const handleKeyDown = e => {
  if (e.key === 'Delete') {
    if (selectedEdge.value) deleteSelectedEdge();
    else if (selectedNode.value) deleteSelectedNode();
  }
};

const autoLayout = () => {
  if (!graph.value) return;
  const nodes = graph.value.getNodes().map(node => ({id: node.id, label: node.getLabel()}));
  const edges = graph.value.getEdges().map(edge => ({source: edge.getSourceCellId(), target: edge.getTargetCellId()}));
  layoutGraph(nodes, edges).forEach(({id, x, y}) => {
    const node = graph.value.getCellById(id);
    if (node) node.position(x, y);
  });
};

watch(relationType, newType => {
  if (selectedEdge.value) {
    selectedEdge.value.setData({relationType: newType});
    selectedEdge.value.setAttrs(getEdgeAttrs(newType));
    selectedEdge.value.setLabels(getEdgeLabel(newType));
  }
});

onMounted(async () => {
  const res = await api.knowledgeList();
  nextTick(() => {
    if (!container.value) return;
    graph.value = new Graph({container: container.value, width: '100%', height: 700, grid: true, panning: true, mousewheel: true});

    const laidOut = layoutGraph(res.nodeList, res.connectList);
    laidOut.forEach(({id, x, y, label}) => {
      const size = getCircleSize(label);
      graph.value.addNode({id, shape: 'circle', x, y, width: size, height: size, label, attrs: getNodeAttrs(unifiedNodeColor, false, size), data: {fill: unifiedNodeColor}, ports: getPorts()});
    });

    res.connectList.forEach(({id, source, target, relationType}) => {
      graph.value.addEdge({
        id,
        source: {cell: source, port: 'port4'},
        target: {cell: target, port: 'port3'},
        attrs: getEdgeAttrs(relationType),
        labels: getEdgeLabel(relationType),
        data: {relationType},
      });
    });

    graph.value.on('edge:connected', ({edge}) => {
      const type = relationType.value;
      edge.setData({relationType: type});
      edge.setAttrs(getEdgeAttrs(type));
      edge.setLabels(getEdgeLabel(type));
      selectEdge(edge);
    });

    graph.value.on('edge:click', ({edge}) => selectEdge(edge));
    graph.value.on('node:click', ({node}) => selectNode(node));
    graph.value.on('blank:click', () => clearSelection());
    graph.value.on('node:dblclick', ({node}) => {
      editingNode.value = node;
      editNodeLabel.value = node.getLabel();
      editDialogVisible.value = true;
    });

    document.addEventListener('keydown', handleKeyDown);
  });
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown);
  graph.value?.dispose();
  graph.value = null;
});
</script>

<style scoped>
.x6-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  height: 700px;
}
</style>
