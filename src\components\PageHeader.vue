<template>
  <div class="page-header">
    <slot name="page-header-back"></slot>
    <span class="page-title">{{ props.title }}</span>
    <div class="page-header-slot">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import {ArrowLeftBold} from '@element-plus/icons-vue';
const props = defineProps({
  title: {
    type: String,
    required: true,
    default: '',
  },
});
</script>
<style scoped lang="scss">
.page-header {
  display: flex;
  width: 100%;
  height: 80px;
  padding: 20px 24px;
  align-items: center;
  flex-shrink: 0;
  background: #fff;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(29, 29, 29, 0.12);
  background: #fff;
  justify-content: space-between;
  align-items: center;
  .page-title {
    color: #1d1d1d;
    text-align: center;
    font-feature-settings: 'ss01' on, 'cv01' on, 'cv11' on;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-right: 20px;
  }
  .page-header-slot {
    flex: 1;
  }
}
</style>
