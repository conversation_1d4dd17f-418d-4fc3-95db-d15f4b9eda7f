<template>
  <div class="select-color" @click.stop="openColorPicker">
    <color-picker
      ref="colorPickerRef"
      v-model:pureColor="pureColor"
      format="hex6"
      shape="square"
      useType="both"
      v-model:gradientColor="gradientColor"
      @update:pureColor="handlePureColorChange"
      @update:gradientColor="handleGradientColorChange"
      :pickerType="'chrome'"
      :disableHistory="false"
      :disableAlpha="false"
      :roundHistory="false"
      :predefineColor="[]"
      :zIndex="9999"
      :disabled="false"
      :pure="false"
      :gradient="true"
      :picker="true"
      :animation="true"
      :isWidget="false"
      :disableFields="false"
      :acceptLabel="'确定'"
      :cancelLabel="'取消'"
      :resetLabel="'重置'"
      :eyeDropperLabel="'吸管'"
      :historyLabel="'历史'"
      :predefineLabel="'预设'"
      :gradientLabel="'渐变'"
      :solidLabel="'纯色'"
      :teleport="false"
      :appendToBody="false"
      :closeOnClickModal="false"
      :closeOnPressEscape="true"
      :showPicker="isPickerOpen"
      :persistent="true"
      :autoClose="false"
      :closeOnSelect="false"
      :modal="false"
    />
  </div>
</template>
<script setup>
import {ref, computed, watch, onMounted, onUnmounted, nextTick} from 'vue';
import {ColorPicker} from 'vue3-colorpicker';
import 'vue3-colorpicker/style.css';

const props = defineProps({
  initialColor: {
    type: String,
    default: '#71afe5',
  },
  initialMode: {
    type: String,
    default: 'solid',
    validator: value => ['solid', 'gradient'].includes(value),
  },
});

const pureColor = ref(props.initialColor);
const gradientColor = ref('linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 100%)');
const currentMode = ref(props.initialMode);

const currentColor = computed(() => {
  return currentMode.value === 'solid' ? pureColor.value : gradientColor.value;
});

const emit = defineEmits(['update:color', 'modeChange']);

// 初始化颜色函数
function initializeColors() {
  if (props.initialMode === 'gradient') {
    gradientColor.value = props.initialColor;
    currentMode.value = 'gradient';
  } else {
    pureColor.value = props.initialColor;
    currentMode.value = 'solid';
  }
}

// 处理纯色变化
function handlePureColorChange(newColor) {
  console.log('Pure color changed:', newColor);
  currentMode.value = 'solid';
  emit('update:color', newColor);
  emit('modeChange', 'solid');

  // 不关闭弹窗，让用户继续选择
  // isPickerOpen.value 保持 true
}

// 处理渐变颜色变化
function handleGradientColorChange(newGradient) {
  console.log('Gradient color changed:', newGradient);
  currentMode.value = 'gradient';
  emit('update:color', newGradient);
  emit('modeChange', 'gradient');

  // 不关闭弹窗，让用户继续选择
  // isPickerOpen.value 保持 true
}

watch(
  () => props.initialColor,
  newColor => {
    if (props.initialMode === 'gradient') {
      gradientColor.value = newColor;
    } else {
      pureColor.value = newColor;
    }
  }
);

watch(
  () => props.initialMode,
  newMode => {
    currentMode.value = newMode;
    if (newMode === 'gradient') {
      gradientColor.value = props.initialColor;
    } else {
      pureColor.value = props.initialColor;
    }
  }
);

// 控制颜色选择器的显示状态
const isPickerOpen = ref(false);
const colorPickerRef = ref(null);

// 处理点击事件
const handleDocumentClick = (event) => {
  // 如果选择器已打开
  if (isPickerOpen.value) {
    // 获取所有可能的颜色选择器相关元素
    const pickerSelectors = [
      '.vue3-colorpicker',
      '.picker-panel',
      '.color-picker-panel',
      '.vc-colorpicker',
      '.vc-color-picker',
      '.colorpicker-panel',
      '.color-picker-wrapper',
      '.select-color'
    ];

    let isClickInside = false;

    // 检查是否点击在触发元素内
    const triggerElement = colorPickerRef.value?.$el || document.querySelector('.select-color');
    if (triggerElement && triggerElement.contains(event.target)) {
      isClickInside = true;
      console.log('Clicked on trigger element');
    }

    // 检查是否点击在任何颜色选择器面板内
    pickerSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element && element.contains(event.target)) {
          isClickInside = true;
          console.log('Clicked inside picker element:', selector);
        }
      });
    });

    // 检查是否点击在颜色选择器的子元素内
    const clickedElement = event.target;
    if (clickedElement.closest('.vue3-colorpicker') ||
        clickedElement.closest('.select-color') ||
        clickedElement.closest('[class*="color"]') ||
        clickedElement.closest('[class*="picker"]')) {
      isClickInside = true;
      console.log('Clicked inside color picker area');
    }

    // 如果点击在外部，则关闭选择器
    if (!isClickInside) {
      console.log('Clicked outside color picker, closing');
      isPickerOpen.value = false;
    } else {
      console.log('Clicked inside color picker, keeping open');
    }
  }
};

// 打开/切换颜色选择器
const openColorPicker = () => {
  console.log('Toggle color picker:', !isPickerOpen.value);
  isPickerOpen.value = !isPickerOpen.value;
};

onMounted(() => {
  initializeColors();
  emit('update:color', currentColor.value);
  emit('modeChange', currentMode.value);

  // 添加全局点击事件监听
  document.addEventListener('click', handleDocumentClick);
});

onUnmounted(() => {
  // 移除全局点击事件监听
  document.removeEventListener('click', handleDocumentClick);
});

defineExpose({
  pureColor,
  gradientColor,
  currentMode,
  currentColor,
});
</script>
<style scoped lang="scss">
.select-color {
  width: 24px !important;
  height: 24px !important;
  overflow: visible; // 改为 visible 以显示弹窗
  margin-left: 8px;
  position: relative;
  z-index: 1000;
  cursor: pointer;
}

// 全局样式，确保颜色选择器弹窗正确显示
:global(.vue3-colorpicker) {
  z-index: 9999 !important;
  position: fixed !important;
}

:global(.vue3-colorpicker .picker-panel) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e4e7ed !important;
  border-radius: 4px !important;
}

// 防止弹窗内的点击事件冒泡
:global(.vue3-colorpicker *) {
  pointer-events: auto;
}
</style>
