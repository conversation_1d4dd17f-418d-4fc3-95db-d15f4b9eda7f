<template>
  <el-dialog v-model="dialogVisible" title="批量添加" width="50%" class="file-upload-dialog" top="5vh">
    <div class="step-header">
      <el-steps :active="setpActive" process-status="finish" finish-status="success" style="width: 90%; margin: 15px auto">
        <el-step title="文件上传" />
        <el-step title="查看结果" />
      </el-steps>
    </div>
    <div class="file-upload" v-if="setpActive === 1">
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          class="upload-container"
          action="https://your-upload-api.com/upload"
          :auto-upload="false"
          :file-list="fileList"
          :on-change="handleChange"
          :limit="5"
          :on-exceed="handleExceed"
          multiple
          drag
          accept=".csv, .xlsx, .xls"
        >
          <img :src="fileIcon" alt="文件图标" width="48" />
          <div class="el-upload__text">
            点击或将文件拖拽到这里上传
            <br />
            文件只支持 .csv、.xlsx、.xls 格式
          </div>
          <template #file="{file}">
            <div class="upload-fileList">
              <div class="upload-fileList-info">
                <img :src="fileIcon" alt="文件图标" class="upload-fileList-img" />
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ formatFileSize(file.size!) }}</span>
              </div>
              <div class="file-actions">
                <el-icon class="delete-icon" @click.stop="handleDeleteFile(file)">
                  <Close />
                </el-icon>
              </div>
            </div>
          </template>
        </el-upload>
      </div>

      <div class="warm-tips">
        <p>温馨提示</p>
        <ul style="list-style: none">
          <li>1. 仅支持上传结构化数据，以便于我们更好的识别。有合并的单元格的，请处理后再上传</li>
          <li>2. 系统会默认将上传的文件首行作为标题行，第二行开始作为要上传的数据</li>
          <li>3. 若上传的表格Sheet页首行为空或者整体内容为空会上传失败</li>
          <li>4. 最多支持5个Sheet的解析和上传，若您需要上传超过5个Sheet的内容，请拆分为多个Excel文件</li>
          <li>5. 文件大小不超过50 MB</li>
        </ul>
      </div>
    </div>
    <div class="file-outcome" v-if="setpActive === 2">
      <p class="outcome-text">{{ outcomeText }}</p>
      <el-table :data="outcomeTableData" style="width: 100%" max-height="500px">
        <el-table-column prop="date" label="测试问题" />
        <el-table-column prop="name" label="来源" />
        <el-table-column prop="address" label="重复原因" />
      </el-table>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleUpload" v-show="setpActive === 1">下一步</el-button>
      <el-button type="primary" @click="save" v-show="setpActive === 2">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {ref, defineEmits} from 'vue';
import {ElMessage} from 'element-plus';
import {Close} from '@element-plus/icons-vue';
import fileIcon from '@/assets/svgs/fileIcon.svg';
import * as api from '@/api/qnTest/qnTest/index';

const emits = defineEmits(['getTableData', 'resetPagination']);

const dialogVisible = ref(false);
const uploadRef = ref(null);
const fileList = ref<any[]>([]);
const setpActive = ref(1);
const outcomeTableData = ref([]);
const outcomeText = ref('');

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
};

// 文件改变事件
const handleChange = (file: any, fileList_: any[]) => {
  const allowedExtensions = ['csv', 'xlsx', 'xls'];
  const invalidFiles = fileList_.filter((item: any) => {
    const fileExtension = item.name?.split('.').pop().toLowerCase();
    return !allowedExtensions.includes(fileExtension);
  });

  if (invalidFiles.length > 0) {
    ElMessage.warning(`不支持的文件类型：${invalidFiles.map((f: any) => f.name).join(', ')}`);
    fileList_.splice(fileList_.indexOf(file), 1);
    return false;
  }

  fileList.value = fileList_;
};

// 文件超出限制事件
const handleExceed = () => {
  ElMessage.warning('最多上传5个文件！');
};

// 上传按钮点击事件
const handleUpload = async () => {
  if (!fileList.value.length) {
    ElMessage.warning('请先选择文件！');
    return;
  }

  try {
    const formData = new FormData();
    fileList.value.forEach((file: any) => {
      formData.append('files', file.raw);
    });

    const res = await api.askNumberCreateBatch(formData);
    outcomeText.value = res;
    ElMessage.success('文件上传成功');
    setpActive.value = 2;
  } catch (error) {
    ElMessage.error('文件上传失败：' + error);
  }
};

// 打开对话框
const openDialog = () => {
  fileList.value = [];
  setpActive.value = 1;
  outcomeTableData.value = [];
  dialogVisible.value = true;
};

// 删除文件事件
const handleDeleteFile = (file: any) => {
  const index = fileList.value.findIndex((f: any) => f.uid === file.uid);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    ElMessage.warning(`${file.name} 已移除`);
  }
};

// 保存按钮点击事件
const save = () => {
  dialogVisible.value = false;
  emits('resetPagination');
  emits('getTableData');
  ElMessage.success('保存成功');
};

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.file-upload-dialog {
  .step-header {
    margin-bottom: 20px;
  }

  .file-upload {
    .upload-area {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      padding: 24px;
      height: 179px;
      text-align: center;
      margin-bottom: 20px;
    }

    .warm-tips {
      margin-top: 20px;
      padding: 12px;
      border-radius: 4px;
      background-color: #f5f7fa;
      color: #909399;
    }
  }

  .file-outcome {
    .outcome-text {
      color: #666;
      font-size: 16px;
      margin: 30px 0;
    }
  }
}

:deep(.el-upload) {
  width: 100%;
  height: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
  background-color: transparent;
  border: none;
}

:deep(.el-upload-list) {
  margin-top: 30px;
}

.upload-fileList {
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
  transition: all 0.3s;

  &:hover {
    background-color: #eef1f6;
  }

  .upload-fileList-info {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;

    .upload-fileList-img {
      width: 20px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .file-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #606266;
      margin-right: 12px;
    }

    .file-size {
      color: #909399;
      font-size: 13px;
      flex-shrink: 0;
    }
  }

  .file-actions {
    margin-left: 12px;

    .delete-icon {
      cursor: pointer;
      color: #909399;
      transition: color 0.3s;

      &:hover {
        color: #f56c6c;
      }
    }
  }
}

:deep(.el-step__title.is-finish) {
  color: #2744d6;
}

:deep(.is-finish .el-step__icon.is-text) {
  color: #fff;
  background: #2744d6;
  border-color: #2744d6;
}

:deep(.is-success .el-step__icon.is-text) {
  color: #67c23a;
  border-color: #67c23a;
}

:deep(.el-step__title.is-success) {
  color: #67c23a;
}
</style>
