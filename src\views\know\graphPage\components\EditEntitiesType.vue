<template>
  <div>
    <el-drawer v-model="drawer" :with-header="false" width="35%">
      <div class="drawer-header">
        <span class="drawer-header-title">编辑实体类型</span>
        <div>
          <el-button @click="drawer = false">取消</el-button>
        </div>
      </div>
      <div class="drawer-warning">
        <el-icon color="#E6A23C"><WarningFilled /></el-icon>
        <span>注意事项：编辑实体类型后，图谱现有的实体会被统一替换，已经存在图谱中的实体不可删除</span>
      </div>
      <div class="drawer-search">
        <el-input placeholder="搜索实体类型" v-model="searchQuery" :prefix-icon="Search" style="width: 200px" clearable @input="searchQueryChange"></el-input>
      </div>

      <div class="entity-table">
        <el-table :data="entities" style="width: 100%" max-height="50vh">
          <el-table-column prop="name" label="实体类型" align="center">
            <template #default="scope">
              <template v-if="editingId === scope.row.id">
                <el-input v-model="editingForm.name" placeholder="请输入实体类型名称" size="small" />
              </template>
              <span v-else>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实体颜色" align="center">
            <template #default="scope">
              <template v-if="editingId === scope.row.id">
                <el-color-picker v-model="editingForm.color" :predefine="predefineColors" show-alpha />
              </template>
              <div v-else class="color-box" :style="{backgroundColor: scope.row.color}"></div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180">
            <template #default="scope">
              <template v-if="editingId === scope.row.id">
                <el-button link type="primary" @click="saveEdit(scope.row)">保存</el-button>
                <el-button link @click="cancelEdit">取消</el-button>
              </template>
              <template v-else>
                <el-button link type="primary" @click="startEdit(scope.row)">编辑</el-button>
                <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[5, 10, 20]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </div>

        <div class="add-entity" @click="addNewRow">
          <el-button type="primary" link class="add-button">+ 添加</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, reactive, nextTick} from 'vue';
import {WarningFilled, Search} from '@element-plus/icons-vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import * as api from '@/api/know/graph';
import {debounce} from '@/utils/tool';

const emits = defineEmits(['getKnowledgeData']);

// 状态管理
const drawer = ref(false);
const searchQuery = ref('');
const editingId = ref(null);
const entities = ref([]);
const editingForm = reactive({id: null, name: '', color: '#409EFF'});
const predefineColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#000000', '#FF85C0', '#722ED1', '#13C2C2', '#52C41A'];

// 分页相关
const pagination = reactive({
  currentPage: 1,
  pageSize: 5,
  total: 0,
});
const searchQueryChange = debounce(() => {
  pagination.currentPage = 1;
  getTagTypeTextList();
}, 1000);

// 获取实体类型列表
const getTagTypeTextList = async () => {
  try {
    const res = await api.graphNodePage({
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      name: searchQuery.value,
    });
    entities.value = res.list;
    pagination.total = res.total;
  } catch (error) {}
};

// 分页事件
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getTagTypeTextList();
};
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  getTagTypeTextList();
};

// 编辑相关
const startEdit = (entity: any) => {
  editingId.value = entity.id;
  editingForm.id = entity.id;
  editingForm.name = entity.name;
  editingForm.color = entity.color;
};
const saveEdit = async (row: any) => {
  if (!editingForm.name.trim()) {
    ElMessage.warning('请输入实体类型名称');
    return;
  }
  if (editingForm.id) {
    const params = {
      color: editingForm.color,
      id: row.id,
      name: editingForm.name,
    };
    await api.updateGraphNode(params);
    ElMessage.success('更新成功');
  } else {
    await api.createGraphNode(editingForm);
    ElMessage.success('创建成功');
  }
  getTagTypeTextList();
  emits('getKnowledgeData');
  editingId.value = null;
};
const cancelEdit = () => {
  if (!editingForm.id) {
    entities.value.pop(); // 新增未保存则移除
  }
  editingId.value = null;
};
const handleDelete = (entity: any) => {
  ElMessageBox.confirm('确定要删除这个实体类型吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await api.deleteGraphNode([entity.id]);
      ElMessage.success('删除成功');
      getTagTypeTextList();
    })
    .catch(() => {});
};
const addNewRow = () => {
  const newEntity = {name: '', color: '#909399'};
  entities.value.push(newEntity);
  nextTick(() => {
    startEdit(newEntity);
  });
};
// 打开抽屉
const openDrawer = async () => {
  await getTagTypeTextList();
  drawer.value = true;
};
defineExpose({openDrawer});
</script>

<style scoped lang="scss">
.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20 20px 0px 20px;
  margin-bottom: 32px;
  .drawer-header-title {
    font-size: 16px;
    color: #76727b;
  }
}
.drawer-warning {
  display: flex;
  align-items: flex-start;
  padding: 8px 20px;
  margin-bottom: 16px;
  background-color: #fdf6ec;
  border-radius: 4px;
  font-size: 14px;
  color: #e6a23c;

  .el-icon {
    margin-right: 8px;
    margin-top: 2px;
  }
}

.drawer-search {
  padding: 0 20px;
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.entity-table {
  padding: 0 20px 20px;

  :deep(.el-table) {
    border: 1px solid #fff;
    border-radius: 4px;

    th {
      background-color: #fff;
      color: #606266;
      font-weight: 500;
    }

    .cell {
      padding: 0 8px;
    }
  }
}

.color-box {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  vertical-align: middle;
  border: 1px solid #dcdfe6;
}

.add-entity {
  margin-top: 16px;
  padding: 6px 0;
  text-align: center;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #409eff;
    background-color: #f5f7fa;
  }

  .add-button {
    color: #409eff;
    font-size: 14px;
  }
}

.add-entity-form {
  margin-top: 16px;
  padding: 16px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;

  .el-form-item {
    margin-bottom: 0;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding-right: 10px;
}

:deep(.el-color-picker) {
  vertical-align: middle;
}
</style>
