<template>
  <PageHeader title="主题模板">
    <template #page-header-back>
      <el-icon class="header-back-icon" @click="goBack"><ArrowLeftBold /></el-icon>
    </template>
    <template #default>
      <div class="content-header">
        <div></div>
        <el-button type="primary" class="create-btn" @click="handleSaveTheme" :loading="saveLoading">保存主题</el-button>
      </div>
    </template>
  </PageHeader>
  <el-card class="content-card">
    <div class="content">
      <div class="content-view" ref="contentViewRef" :class="{'content-view-collapsed': showConfig, 'single-column': shouldUseSingleColumn}" @click="handleContentViewClick" :style="getBackgroundStyle()">
        <div class="content-view-topBg" :style="{backgroundImage: `url(${visualStore.pageConfig.pageLayout.pageTopImg})`}"></div>
        <h1 v-show="visualStore.pageConfig.pageLayout.pageTitle == 'show'" :style="getTitleStyle()">{{ visualStore.pageConfig.basicConfig.themeTitle }}</h1>
        <!-- 图表网格容器 -->
        <div class="charts-grid">
          <visualView ref="visualViewRef"
          :chartOption="visualStore.chartConfig[item.value]"
          :chartType="item.value"
          @click="handleVisualViewClick(item)"
          :key="index"
          v-for="(item, index) in chartOptions"
          class="chart-item" />
        </div>

        <!-- 底部图片 - 跟随内容流，在所有图表下方 -->
        <div class="content-view-bottomBg" :style="{backgroundImage: `url(${visualStore.pageConfig.pageLayout.pageBottomImg})`}"></div>
      </div>
      <transition name="slide">
        <div class="content-config" v-show="showConfig">
          <div class="config-header">
            <h3>{{ configTitle }}</h3>
            <el-icon class="close-btn" @click="isShowClick">
              <Close />
            </el-icon>
          </div>
          <div class="config-content">
            <div v-if="configType === 'page'">
              <!-- 页面配置内容 -->
              <ThemeConfig />
            </div>
            <div v-else-if="configType === 'chart'">
              <!-- <h4>图形配置</h4>
              <p>这里是图表相关的配置选项</p> -->
              <!-- 图表配置内容 -->
              <ChartConfig :componentName="componentName"/>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </el-card>


</template>

<script setup lang="ts">
import {ref, computed, onMounted, onUnmounted, nextTick} from 'vue';
import {useRouter} from 'vue-router';
import {ArrowLeftBold, Close} from '@element-plus/icons-vue';
import PageHeader from '@/components/PageHeader.vue';
import visualView from './components/visualView/VisualView.vue';
import ThemeConfig from './components/themeConfig/ThemeConfig.vue';
import ChartConfig from './components/ChartConfig/index.vue';
import {useVisualStore} from '@/store/modules/visual';
import chartJSON from './chartJSON.json';
import { saveTheme, updateTheme, getThemeDetail, type ThemeData } from '@/api/visualTheme';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
const visualStore: any = useVisualStore();

// 保存主题相关数据
const saveLoading = ref(false);
const currentThemeId = ref<string | null>(null); // 当前编辑的主题ID

const showConfig = ref(false);
const configType = ref('page'); // 'page' 或 'chart'
const visualViewRef = ref(null);

// 图表配置选项列表
const chartOptions = ref([
  {label: '柱状图', value: 'barConfig'},
  {label: '条形图', value: 'barHorizontalConfig'},
  {label: '折线图', value: 'lineConfig'},
  {label: '饼状图', value: 'pieConfig'},
  {label: '漏斗图', value: 'funnelConfig'},
  {label: '雷达图', value: 'radarConfig'},
  {label: '仪表图', value: 'gaugeConfig'},
]);
// 当前选中的图表配置组件名称
let componentName = ref('barConfig');

// 容器宽度监听
const contentViewRef = ref<HTMLElement>();
const containerWidth = ref(0);
const shouldUseSingleColumn = ref(false);

// 监听容器宽度变化
const observeContainerWidth = () => {
  if (!contentViewRef.value) return;

  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      containerWidth.value = entry.contentRect.width;
      // 当容器宽度小于800px时，使用单列布局
      shouldUseSingleColumn.value = entry.contentRect.width < 800;
    }
  });

  resizeObserver.observe(contentViewRef.value);

  // 返回清理函数
  return () => {
    resizeObserver.disconnect();
  };
};


// 计算配置标题
const configTitle = computed(() => {
  return configType.value === 'page' ? '主题样式' : '图形配置';
});

// 处理content-view区域点击
const handleContentViewClick = event => {
  // 检查点击事件是否来自VisualView组件
  if (event.target.closest('.visual-view-card')) {
    return; // 如果点击的是VisualView组件，不处理
  }

  configType.value = 'page';
  showConfig.value = true;
};

// 处理VisualView组件点击
const handleVisualViewClick = (data) => {
  configType.value = 'chart';
  showConfig.value = true;
  componentName.value = data.value;
  visualStore.chartType = data.value;
};

// 新建主题
// 收集当前所有配置数据
const collectThemeData = (): ThemeData => {
  // 从主题样式中获取主题名称
  const themeName = visualStore.pageConfig.basicConfig.themeTitle || '未命名主题';

  return {
    id: currentThemeId.value, // 编辑模式时传入ID
    name: themeName,
    description: '', // 暂时不设置描述
    chartConfigs: {
      barConfig: JSON.parse(JSON.stringify(visualStore.chartConfig.barConfig)),
      barHorizontalConfig: JSON.parse(JSON.stringify(visualStore.chartConfig.barHorizontalConfig)),
      lineConfig: JSON.parse(JSON.stringify(visualStore.chartConfig.lineConfig)),
      pieConfig: JSON.parse(JSON.stringify(visualStore.chartConfig.pieConfig)),
      funnelConfig: JSON.parse(JSON.stringify(visualStore.chartConfig.funnelConfig)),
      radarConfig: JSON.parse(JSON.stringify(visualStore.chartConfig.radarConfig)),
      gaugeConfig: JSON.parse(JSON.stringify(visualStore.chartConfig.gaugeConfig))
    },
    pageConfig: {
      basicConfig: JSON.parse(JSON.stringify(visualStore.pageConfig.basicConfig)),
      globalStyle: JSON.parse(JSON.stringify(visualStore.pageConfig.globalStyle)),
      pageLayout: JSON.parse(JSON.stringify(visualStore.pageConfig.pageLayout))
    }
  };
};

// 保存主题
const handleSaveTheme = async () => {
  try {
    // 检查主题名称是否为空
    const themeName = visualStore.pageConfig.basicConfig.themeTitle;
    if (!themeName || themeName.trim() === '') {
      ElMessage.warning('请先在主题配置中设置主题名称');
      return;
    }

    saveLoading.value = true;

    // 收集配置数据
    const themeData = collectThemeData();

    console.log('准备保存的主题数据:', themeData);

    let response;

    // 根据是否有主题ID判断是新建还是编辑
    if (currentThemeId.value) {
      // 编辑模式：调用更新接口
      response = await updateTheme(currentThemeId.value, themeData);
    } else {
      // 新建模式：调用创建接口
      response = await saveTheme(themeData);
    }

    if (response.code === 200) {
      ElMessage.success(currentThemeId.value ? '主题更新成功！' : '主题保存成功！');

      // 跳转到可视化主题管理页面
      router.push('/visual/visualization');
    } else {
      ElMessage.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('保存主题失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saveLoading.value = false;
  }
};

// 页面初始化
const initializePage = async () => {
  const mode = route.query.mode as string;
  const themeId = route.query.themeId as string;

  console.log('页面初始化参数:', { mode, themeId });

  if (mode === 'edit' && themeId) {
    // 编辑模式：设置当前主题ID并加载配置
    currentThemeId.value = themeId;
    await loadThemeConfig(themeId);
  } else if (mode === 'copy' && themeId) {
    // 复制模式：不设置主题ID，加载配置但作为新主题保存
    currentThemeId.value = null;
    await loadThemeConfig(themeId, true);
  } else if (mode === 'new') {
    // 新建模式：清空主题ID，重置为默认配置
    currentThemeId.value = null;
    // 调用 store 的重置方法，恢复默认配置
    visualStore.resetToDefault();
    console.log('新建主题，已重置为默认配置');
  } else {
    // 默认情况：清空主题ID，重置为默认配置
    currentThemeId.value = null;
    // 调用 store 的重置方法，恢复默认配置
    visualStore.resetToDefault();
    console.log('使用默认配置');
  }
};

// 加载主题配置
const loadThemeConfig = async (themeId: string, isCopy = false) => {
  try {
    console.log('加载主题配置:', themeId);

    const response = await getThemeDetail(themeId);

    if (response.code === 200) {
      const themeData = response.data;

      // 将主题配置应用到store
      if (themeData.chartConfigs) {
        Object.keys(themeData.chartConfigs).forEach(chartType => {
          if (visualStore.chartConfig[chartType]) {
            visualStore.chartConfig[chartType] = themeData.chartConfigs[chartType];
          }
        });
      }

      if (themeData.pageConfig) {
        if (themeData.pageConfig.basicConfig) {
          visualStore.pageConfig.basicConfig = themeData.pageConfig.basicConfig;
        }
        if (themeData.pageConfig.globalStyle) {
          visualStore.pageConfig.globalStyle = themeData.pageConfig.globalStyle;
        }
        if (themeData.pageConfig.pageLayout) {
          visualStore.pageConfig.pageLayout = themeData.pageConfig.pageLayout;
        }
      }

      // 如果是复制模式，修改主题名称为副本
      if (isCopy) {
        visualStore.pageConfig.basicConfig.themeTitle = `${themeData.name} - 副本`;
      }

      ElMessage.success('主题配置加载成功');
    } else {
      ElMessage.error(response.message || '加载主题配置失败');
    }
  } catch (error) {
    console.error('加载主题配置失败:', error);
    ElMessage.error('加载主题配置失败');
  }
};

// 组件挂载时初始化
onMounted(() => {
  initializePage();

  // 启动容器宽度监听
  nextTick(() => {
    const cleanup = observeContainerWidth();

    // 在组件卸载时清理
    onUnmounted(() => {
      if (cleanup) cleanup();
    });
  });
});

const isShowClick = () => {
  showConfig.value = !showConfig.value;
};

const goBack = () => {
  router.replace('/visual/visualization');
};

const getBackgroundStyle = () => {
  const colorMode = visualStore.pageConfig.pageLayout.colorMode;

  if (colorMode === 'gradient') {
    // 渐变模式，使用渐变色值
    return {
      background: visualStore.pageConfig.pageLayout.gradientColor,
      padding: visualStore.pageConfig.pageLayout.pagePadding,
    };
  } else {
    // 纯色模式，使用纯色值
    return {
      background: visualStore.pageConfig.pageLayout.pageBackgroundColor,
      padding: visualStore.pageConfig.pageLayout.pagePadding,
    };
  }
};

// 获取标题样式
const getTitleStyle = () => {
  // 确保 titleFont 对象存在
  const titleFont = visualStore.pageConfig.basicConfig.titleFont || {
    fontFamily: "Arial",
    fontSize: 16,
    fontWeight: "normal",
    fontStyle: "normal",
    textAlign: "center"
  };

  return {
    fontFamily: titleFont.fontFamily || 'Arial',
    fontSize: `${titleFont.fontSize || 50}px`, // 保持较大的字号作为标题
    fontWeight: titleFont.fontWeight || 'normal',
    fontStyle: titleFont.fontStyle || 'normal',
    textAlign: titleFont.textAlign || 'center',
    // 保持原有的样式
    position: 'relative' as const,
    zIndex: 1,
    margin: '20px 0',
    color: '#333'
  };
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: row;
  position: relative;
  height: calc(100vh - 180px); // 减去页头和卡片边距的高度

  .content-view {
    position: relative;
    flex: 1;
    height: 100%;
    background: #f5f7fa;
    cursor: pointer;
    overflow-y: auto; // 添加垂直滚动条
    overflow-x: hidden; // 隐藏水平滚动条

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;

      &:hover {
        background: #a8a8a8;
      }
    }

    // 内容区域的最小高度（padding 通过 :style 动态设置）
    min-height: 100%;
    box-sizing: border-box;

    // 图表网格布局 - 一行显示两个，宽度自适应
    .charts-grid {
      display: grid;
      grid-template-columns: 1fr 1fr; // 固定两列，等宽分配
      gap: 20px; // 图表之间的间距
      position: relative;
      z-index: 2; // 确保在背景图之上
      margin-top: 0; // 不需要额外的上边距，由动态 padding 控制


      .chart-item {
        width: 100%; // 占满网格单元格的宽度
        height: 300px; // 固定高度，避免无限增长
        box-sizing: border-box;
      }
    }

    // 当配置面板显示时的自适应处理
    &.content-view-collapsed {
      .charts-grid {
        // 当配置面板显示时，容器宽度变小，在中等屏幕尺寸下改为单列
        @media (max-width: 1200px) {
          grid-template-columns: 1fr; // 单列显示，确保图表有足够宽度
          gap: 15px;

          .chart-item {
            min-height: 280px; // 稍微减少高度
          }
        }

        // 在较大屏幕上保持双列，但减少间距
        @media (min-width: 1201px) {
          gap: 15px; // 减少间距以节省空间
        }
      }
    }

    // 基于JavaScript监听的动态单列布局
    &.single-column {
      .charts-grid {
        grid-template-columns: 1fr !important; // 强制单列显示
        gap: 15px !important;

        .chart-item {
          height: 280px !important; // 固定高度
        }
      }
    }

    // 当屏幕宽度较小时，无论配置面板是否显示都改为单列
    @media (max-width: 768px) {
      .charts-grid {
        grid-template-columns: 1fr !important; // 强制单列显示
        gap: 15px !important;

        .chart-item {
          height: 250px !important; // 固定高度
        }
      }
    }
    .content-view-topBg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40%;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      z-index: 1;
      -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
      mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
    }
    .content-view-bottomBg {
      position: relative; // 改为相对定位，跟随内容流
      width: 100%;
      height: 200px; // 固定高度，替代之前的40%
      margin-top: 40px; // 与图表保持一定距离
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
      z-index: 1;
      -webkit-mask-image: linear-gradient(to top, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
      mask-image: linear-gradient(to top, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
    }
  }
  .content-config {
    width: 25%;
    height: 100%;
    background: #fff;
    border-left: 1px solid #e4e7ed;
    position: relative;
    overflow-y: auto;

    .config-header {
      position: sticky;
      top: 0;
      z-index: 2;
      background: #fff;
      padding: 16px;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .close-btn {
        cursor: pointer;
        font-size: 18px;
        color: #909399;
        transition: all 0.3s ease;

        &:hover {
          color: #f56c6c;
          transform: scale(1.1);
        }
      }
    }

    .config-content {
      padding: 16px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
      }

      p {
        margin: 0;
        font-size: 12px;
        color: #909399;
        line-height: 1.5;
      }
    }
  }
}

// 滑动动画
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}
</style>
