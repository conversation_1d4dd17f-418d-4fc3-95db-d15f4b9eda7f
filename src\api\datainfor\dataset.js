import request from '@/config/axios'
import { getAccessToken } from '@/utils/auth'

// {{baseUrl}}/admin-api/dataset/list?name=zls
// 获取数据集列表
export function getDataSetList(params) {
  return request.get({
    url: '/dataset/pageList',
    params
  })
}

// 获取数据表或者excel详情
// {{baseUrl}}/basic/dataset/getDataSetFieldsByTableOrSheetId?dataSourceId=1933449405772836866&tableNameOrSheetId=datasource
export function getDataSourceDetail(params) {
  return request.get({
    url: '/dataset/getDataSetFieldsByTableOrSheetId',
    params
  })
}

// 创建数据集
// /basic/dataset/create
export function postCreateDataSet(params) {
  return request.post({
    url: '/dataset/create',
    data: params
  })
}
// 删除数据集
// /basic/dataset/deleteById/1932739291801583600
export function getDeleteDataSet(params) {
  return request.delete({
    url: `/dataset/deleteById/${params.id}`,
    
  })
}

// 修改数据集
// /basic/dataset/update
export function postUpdateDataSet(params) {
  return request.post({
    url: '/dataset/update',
    data: params
  })
}

// 获取数据集详情
// /basic/dataset/dataSetDetailInfo
export function getDataSetDetail(params) {
  return request.get({
    url: '/dataset/dataSetDetailInfo',
    params
  })
}
// 预览数据集字段
// {{baseUrl}}/basic/dataset/dataPreview
export function postDataSetPreview(params) {
  return request.post({
    url: '/dataset/dataPreview',
    data: params
  })
}

// 获取业务名称接口
// /basic/knowledge/definitions/page

export function getBusinessName(params) {
  return request.get({
    url: '/knowledge/definitions/page',
    params
  })
}

// 获取维度枚举的接口
// /basic/dataset/getColumnAllDimensionOptions

export function getDimensionOptions(params) {
  return request.get({
    url: '/dataset/getColumnAllDimensionOptions',
    params
  })
}


// 问数配置相关接口

// 获取智能问数数据集列表
///basic/dataset/getAiAskNumberList

export function getAiAskNumberList(params) {
  return request.get({
    url: '/dataset/getAiAskNumberList',
    params
  })
}

// 更新智能问数标识
// /basic/dataset/updateAiAskNumberFlag
export function updateAiAskNumberFlag(params) {
  return request.post({
    url: '/dataset/updateAiAskNumberFlag',
    data: params
  })
}
// 查询数据集智能问数信息
// /basic/dataset/getAiAskNumberInfo
export function getAiAskNumberInfo(params) {
  return request.get({
    url: '/dataset/getAiAskNumberInfo',
    params
  })
}

// 获取选中数据集的智能问数字段配置
// /basic/dataset/getSelectedDataSetAiAskNumberFields
export function getSelectedDataSetAiAskNumberFields(params) {
  return request.get({
    url: '/dataset/getSelectedDataSetAiAskNumberFields',
    params
  })
}

// 保存选中数据集的智能问数字段配置
// /basic/dataset/saveSelectedDataSetAiAskNumberFields
export function saveSelectedDataSetAiAskNumberFields(data) {
  return request.post({
    url: '/dataset/saveSelectedDataSetAiAskNumberFields',
    data
  })
}



// 生成数据集智能问数易混描述
// /basic/dataset/genAiAskNumberEasyConfuseDesc
export function genAiAskNumberEasyConfuseDesc(params) {
  return request.get({
    url: '/dataset/genAiAskNumberEasyConfuseDesc',
    params
  })
}

// 保存特殊规则
// /basic/dataset/updateSpecialRules
export function updateSpecialRules(data) {
  return request.post({
    url: '/dataset/updateSpecialRules',
    data
  })
}



